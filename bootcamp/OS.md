# 进程

## 本质

进程是程序在计算机上的一次执行实例，是操作系统进行资源分配和调度的基本单位。每个进程拥有独立的地址空间和系统资源，实现故障隔离。

- 独立性：拥有独立的虚拟地址空间
- 动态性：生命周期包含创建、执行、终止
- 并发性：多个进程可同时运行
- 结构性：由程序代码、数据、执行上下文组成

## 内部结构

### 进程控制块(PCB)

操作系统通过进程控制块管理所有进程。Linux中对应 `task_struct`结构体：

```c
struct task_struct {
    pid_t pid;                // 进程ID
    struct mm_struct *mm;     // 内存管理信息
    struct files_struct *files; // 文件描述符表
    struct sighand_struct *sighand; // 信号处理
    struct nsproxy *nsproxy;  // 命名空间
    // ...
};
```

### 地址空间

```
+---------------------+ 0x7ffffffff000
|     用户态栈        |
+---------------------+
|      映射区         | (mmap)
+---------------------+
|       堆           | ← brk增长
+---------------------+
|     .data/.bss      |
+---------------------+
|      .text          |
+---------------------+ 0x400000
```

查看进程内存映射：

```bash
cat /proc/<pid>/maps
```

## 生命周期

### 进程创建

Linux使用 `fork()`和 `clone()`系统调用创建进程：

```c
pid_t fork(void);
pid_t clone(unsigned long clone_flags, void *child_stack);
```

Go语言中的进程创建：

```go
package main

import (
    "fmt"
    "os"
    "os/exec"
)

func main() {
    // 方法1: 使用命令
    cmd := exec.Command("echo", "Hello Process")
    output, _ := cmd.Output()
    fmt.Print(string(output))

    // 方法2: 直接系统调用(需要CGO)
    /*
    pid, _, _ := syscall.Syscall(syscall.SYS_FORK, 0, 0, 0)
    if pid == 0 {
        // 子进程
        syscall.SYS_EXECVE(...)
    }
    */
}
```

### 进程终止

进程终止流程：

1. 执行 `exit()`系统调用
1. 释放资源
1. 向父进程发送SIGCHLD信号
1. 进入僵尸态，等待父进程回收

处理僵尸进程：

```bash
# 查看僵尸进程
ps -eo pid,stat,comm | grep Z

# 方法1: 终止父进程
kill <parent_pid>

# 方法2: 使用wait系统调用
sudo gdb -p <parent_pid>
(gdb) call waitpid(-1, 0, 0)
```

## 调度机制

### 调度目标

CPU利用率最大化：减少CPU空闲时间
吞吐量最大化：单位时间内完成更多进程
周转时间最小化：从提交到完成的总时间
等待时间最小化：进程在就绪队列的等待时间
响应时间最小化：交互式系统的快速响应

### 调度层次

长程调度：决定哪些进程进入内存（作业调度）
中程调度：内存和外存间进程的对换
短程调度：CPU分配给就绪进程（进程调度）

### 调度算法

| 算法   | 平均等待时间 | 响应时间 | 公平性 | 实现复杂度 | 适用场景 |
| ------ | ------------ | -------- | ------ | ---------- | -------- |
| FCFS   | 中等         | 差       | 好     | 简单       | 批处理   |
| SJF    | 最短         | 中等     | 一般   | 中等       | 批处理   |
| SRTF   | 最短         | 中等     | 一般   | 复杂       | 实时系统 |
| RR     | 中等         | 好       | 好     | 简单       | 分时系统 |
| 优先级 | 可变         | 可变     | 差     | 中等       | 实时系统 |
| MLFQ   | 中等         | 好       | 好     | 复杂       | 通用系统 |

#### 经典调度算法

##### 先来先服务（FCFS, First-Come First-Served）

原理：按进程到达顺序执行，非抢占式

```go
package main

import (
    "fmt"
    "sort"
)

type Process struct {
    ID       int
    Arrival  int  // 到达时间
    Burst    int  // CPU执行时间
    Wait     int  // 等待时间
    Turnaround int // 周转时间
    Completion int // 完成时间
}

// FCFS调度算法实现
func FCFS(processes []Process) []Process {
    n := len(processes)
    results := make([]Process, n)
    copy(results, processes)

    // 按到达时间排序
    sort.Slice(results, func(i, j int) bool {
        return results[i].Arrival < results[j].Arrival
    })

    currentTime := 0
    for i := 0; i < n; i++ {
        // 如果当前时间小于进程到达时间，CPU空闲
        if currentTime < results[i].Arrival {
            currentTime = results[i].Arrival
        }

        results[i].Completion = currentTime + results[i].Burst
        results[i].Turnaround = results[i].Completion - results[i].Arrival
        results[i].Wait = results[i].Turnaround - results[i].Burst

        currentTime = results[i].Completion
    }

    return results
}

func main() {
    processes := []Process{
        {1, 0, 5, 0, 0, 0},
        {2, 1, 3, 0, 0, 0},
        {3, 2, 8, 0, 0, 0},
        {4, 4, 6, 0, 0, 0},
    }

    results := FCFS(processes)
    fmt.Println("FCFS调度结果:")
    fmt.Printf("PID\t到达\t执行\t完成\t周转\t等待\n")
    totalWait := 0
    totalTurnaround := 0

    for _, p := range results {
        fmt.Printf("%d\t%d\t%d\t%d\t%d\t%d\n",
            p.ID, p.Arrival, p.Burst, p.Completion, p.Turnaround, p.Wait)
        totalWait += p.Wait
        totalTurnaround += p.Turnaround
    }

    fmt.Printf("平均等待时间: %.2f\n", float64(totalWait)/float64(len(results)))
    fmt.Printf("平均周转时间: %.2f\n", float64(totalTurnaround)/float64(len(results)))
}
```

优点：

- 实现简单
- 公平性好

缺点：

- 平均等待时间长
- 护航效应：短进程被长进程阻塞

##### 短作业优先（SJF, Shortest Job First）

原理：优先执行执行时间最短的进程

```go
// 非抢占式SJF
func SJF(processes []Process) []Process {
    n := len(processes)
    results := make([]Process, n)
    copy(results, processes)

    // 按到达时间排序
    sort.Slice(results, func(i, j int) bool {
        return results[i].Arrival < results[j].Arrival
    })

    currentTime := 0
    completed := 0
    isCompleted := make([]bool, n)

    for completed < n {
        // 找到已到达且未完成的最短作业
        minBurst := 999999
        idx := -1

        for i := 0; i < n; i++ {
            if !isCompleted[i] && results[i].Arrival <= currentTime {
                if results[i].Burst < minBurst {
                    minBurst = results[i].Burst
                    idx = i
                }
            }
        }

        // 如果没有可执行的进程，跳转到下一个进程到达时间
        if idx == -1 {
            minArrival := 999999
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival > currentTime {
                    if results[i].Arrival < minArrival {
                        minArrival = results[i].Arrival
                    }
                }
            }
            currentTime = minArrival
            continue
        }

        // 执行选中的进程
        results[idx].Completion = currentTime + results[idx].Burst
        results[idx].Turnaround = results[idx].Completion - results[idx].Arrival
        results[idx].Wait = results[idx].Turnaround - results[idx].Burst
        currentTime = results[idx].Completion
        isCompleted[idx] = true
        completed++
    }

    return results
}
```

优点：

- 平均等待时间最短
- 理论最优

缺点：

- 需要预知执行时间（实际中难以做到）
- 长作业饥饿问题

##### 最短剩余时间优先（SRTF, Shortest Remaining Time First）

原理：SJF的抢占式版本

```go
// 抢占式SJF (SRTF)
func SRTF(processes []Process) []Process {
    n := len(processes)
    results := make([]Process, n)
    copy(results, processes)

    remainingTime := make([]int, n)
    for i := 0; i < n; i++ {
        remainingTime[i] = results[i].Burst
    }

    completed := 0
    currentTime := 0
    isCompleted := make([]bool, n)

    for completed < n {
        // 找到已到达且剩余时间最短的进程
        minRemaining := 999999
        shortest := -1

        for i := 0; i < n; i++ {
            if !isCompleted[i] && results[i].Arrival <= currentTime {
                if remainingTime[i] < minRemaining {
                    minRemaining = remainingTime[i]
                    shortest = i
                }
            }
        }

        // 如果没有进程可执行，跳转时间
        if shortest == -1 {
            minArrival := 999999
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival > currentTime {
                    if results[i].Arrival < minArrival {
                        minArrival = results[i].Arrival
                    }
                }
            }
            currentTime = minArrival
            continue
        }

        // 执行一个时间单位
        remainingTime[shortest]--
        currentTime++

        // 如果进程完成
        if remainingTime[shortest] == 0 {
            isCompleted[shortest] = true
            completed++
            results[shortest].Completion = currentTime
            results[shortest].Turnaround = currentTime - results[shortest].Arrival
            results[shortest].Wait = results[shortest].Turnaround - results[shortest].Burst
        }
    }

    return results
}
```

##### 时间片轮转（RR, Round Robin）

原理：每个进程分配固定时间片，轮转执行

```go
// 时间片轮转调度
func RoundRobin(processes []Process, timeQuantum int) []Process {
    n := len(processes)
    results := make([]Process, n)
    copy(results, processes)

    // 保存原始信息用于计算
    arrival := make([]int, n)
    burst := make([]int, n)
    for i := 0; i < n; i++ {
        arrival[i] = results[i].Arrival
        burst[i] = results[i].Burst
    }

    remainingTime := make([]int, n)
    copy(remainingTime, burst)

    queue := make([]int, 0)  // 就绪队列
    currentTime := 0
    completed := 0
    completionTime := make([]int, n)
    isCompleted := make([]bool, n)

    // 初始化队列
    for i := 0; i < n && results[i].Arrival <= currentTime; i++ {
        queue = append(queue, i)
    }

    for completed < n {
        if len(queue) == 0 {
            // 找到下一个到达的进程
            minArrival := 999999
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival > currentTime {
                    if results[i].Arrival < minArrival {
                        minArrival = results[i].Arrival
                    }
                }
            }
            currentTime = minArrival
            // 添加新到达的进程到队列
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival <= currentTime {
                    // 检查是否已在队列中
                    found := false
                    for _, pid := range queue {
                        if pid == i {
                            found = true
                            break
                        }
                    }
                    if !found {
                        queue = append(queue, i)
                    }
                }
            }
            continue
        }

        // 取出队首进程
        currentProcess := queue[0]
        queue = queue[1:]

        // 执行时间片或剩余时间
        execTime := timeQuantum
        if remainingTime[currentProcess] < timeQuantum {
            execTime = remainingTime[currentProcess]
        }

        currentTime += execTime
        remainingTime[currentProcess] -= execTime

        // 添加新到达的进程到队列
        for i := 0; i < n; i++ {
            if !isCompleted[i] && results[i].Arrival <= currentTime && results[i].Arrival > results[currentProcess].Arrival {
                // 检查是否已在队列中
                found := false
                for _, pid := range queue {
                    if pid == i {
                        found = true
                        break
                    }
                }
                if !found {
                    queue = append(queue, i)
                }
            }
        }

        // 如果进程完成
        if remainingTime[currentProcess] == 0 {
            isCompleted[currentProcess] = true
            completed++
            completionTime[currentProcess] = currentTime
            results[currentProcess].Completion = currentTime
            results[currentProcess].Turnaround = currentTime - arrival[currentProcess]
            results[currentProcess].Wait = results[currentProcess].Turnaround - burst[currentProcess]
        } else {
            // 进程未完成，重新加入队列末尾
            queue = append(queue, currentProcess)
        }
    }

    return results
}
```

优点：

- 响应时间好
- 公平性好
- 适合交互式系统

缺点：

- 时间片选择影响性能
- 进程切换开销

##### 优先级调度（Priority Scheduling）

原理：按进程优先级调度，数值越小优先级越高

```go
type PriorityProcess struct {
    Process
    Priority int
}

// 非抢占式优先级调度
func PriorityScheduling(processes []PriorityProcess) []PriorityProcess {
    n := len(processes)
    results := make([]PriorityProcess, n)
    copy(results, processes)

    // 按到达时间和优先级排序
    sort.Slice(results, func(i, j int) bool {
        if results[i].Arrival == results[j].Arrival {
            return results[i].Priority < results[j].Priority
        }
        return results[i].Arrival < results[j].Arrival
    })

    currentTime := 0
    completed := 0
    isCompleted := make([]bool, n)

    for completed < n {
        // 找到已到达且未完成的最高优先级进程
        highestPriority := 999999
        idx := -1

        for i := 0; i < n; i++ {
            if !isCompleted[i] && results[i].Arrival <= currentTime {
                if results[i].Priority < highestPriority {
                    highestPriority = results[i].Priority
                    idx = i
                }
            }
        }

        // 如果没有可执行的进程，跳转时间
        if idx == -1 {
            minArrival := 999999
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival > currentTime {
                    if results[i].Arrival < minArrival {
                        minArrival = results[i].Arrival
                    }
                }
            }
            currentTime = minArrival
            continue
        }

        // 执行进程
        results[idx].Completion = currentTime + results[idx].Burst
        results[idx].Turnaround = results[idx].Completion - results[idx].Arrival
        results[idx].Wait = results[idx].Turnaround - results[idx].Burst
        currentTime = results[idx].Completion
        isCompleted[idx] = true
        completed++
    }

    return results
}

// 抢占式优先级调度
func PreemptivePriority(processes []PriorityProcess) []PriorityProcess {
    n := len(processes)
    results := make([]PriorityProcess, n)
    copy(results, processes)

    remainingTime := make([]int, n)
    for i := 0; i < n; i++ {
        remainingTime[i] = results[i].Burst
    }

    completed := 0
    currentTime := 0
    isCompleted := make([]bool, n)
    lastExecuted := -1

    for completed < n {
        // 找到已到达且未完成的最高优先级进程
        highestPriority := 999999
        highestPriorityIdx := -1

        for i := 0; i < n; i++ {
            if !isCompleted[i] && results[i].Arrival <= currentTime {
                if results[i].Priority < highestPriority {
                    highestPriority = results[i].Priority
                    highestPriorityIdx = i
                }
            }
        }

        // 如果没有进程可执行，跳转时间
        if highestPriorityIdx == -1 {
            minArrival := 999999
            for i := 0; i < n; i++ {
                if !isCompleted[i] && results[i].Arrival > currentTime {
                    if results[i].Arrival < minArrival {
                        minArrival = results[i].Arrival
                    }
                }
            }
            currentTime = minArrival
            continue
        }

        // 如果有更高优先级的进程到来，进行抢占
        if lastExecuted != -1 && results[highestPriorityIdx].Priority < results[lastExecuted].Priority {
            // 可以实现抢占逻辑
        }

        // 执行一个时间单位
        remainingTime[highestPriorityIdx]--
        currentTime++
        lastExecuted = highestPriorityIdx

        // 如果进程完成
        if remainingTime[highestPriorityIdx] == 0 {
            isCompleted[highestPriorityIdx] = true
            completed++
            results[highestPriorityIdx].Completion = currentTime
            results[highestPriorityIdx].Turnaround = currentTime - results[highestPriorityIdx].Arrival
            results[highestPriorityIdx].Wait = results[highestPriorityIdx].Turnaround - results[highestPriorityIdx].Burst
        }
    }

    return results
}
```

##### 多级反馈队列（MLFQ, Multilevel Feedback Queue）

```go
type QueueLevel struct {
    processes []Process
    timeQuantum int
    priority int
}

type MLFQScheduler struct {
    queues []QueueLevel
    maxLevels int
}

func NewMLFQ(maxLevels int) *MLFQScheduler {
    queues := make([]QueueLevel, maxLevels)
    for i := 0; i < maxLevels; i++ {
        queues[i] = QueueLevel{
            processes: make([]Process, 0),
            timeQuantum: 1 << uint(i), // 1, 2, 4, 8...
            priority: i,
        }
    }

    return &MLFQScheduler{
        queues: queues,
        maxLevels: maxLevels,
    }
}

func (m *MLFQScheduler) AddProcess(process Process) {
    // 新进程加入最高优先级队列
    m.queues[0].processes = append(m.queues[0].processes, process)
}

func (m *MLFQScheduler) Schedule() (Process, bool) {
    // 从最高优先级队列开始查找
    for i := 0; i < m.maxLevels; i++ {
        if len(m.queues[i].processes) > 0 {
            process := m.queues[i].processes[0]
            m.queues[i].processes = m.queues[i].processes[1:]
            return process, true
        }
    }
    return Process{}, false
}
```

#### 现代调度算法

##### 完全公平调度器（CFS, Completely Fair Scheduler）

Linux内核使用的核心调度算法：

```go
// CFS的核心概念：虚拟运行时间
type CFSProcess struct {
    Process
    vruntime int64  // 虚拟运行时间
    weight   int64  // 权重（优先级相关）
}

// 计算虚拟运行时间
func (p *CFSProcess) updateVruntime(execTime int) {
    p.vruntime += int64(execTime) * 1024 / p.weight
}

// CFS调度器简化的实现
type CFSScheduler struct {
    processes []*CFSProcess
    minHeap   []*CFSProcess  // 最小堆按vruntime排序
}

func (c *CFSScheduler) addProcess(process *CFSProcess) {
    c.processes = append(c.processes, process)
    c.minHeap = append(c.minHeap, process)
    // 维护最小堆性质
    c.heapifyUp(len(c.minHeap) - 1)
}

func (c *CFSScheduler) schedule() *CFSProcess {
    if len(c.minHeap) == 0 {
        return nil
    }

    // 选择vruntime最小的进程
    selected := c.minHeap[0]
    c.minHeap[0] = c.minHeap[len(c.minHeap)-1]
    c.minHeap = c.minHeap[:len(c.minHeap)-1]
    c.heapifyDown(0)

    return selected
}
```

##### 实时调度算法

Rate Monotonic Scheduling (RMS)

```go
// 速率单调调度：周期越短，优先级越高
func RMSSchedule(processes []PeriodicProcess) bool {
    // 按周期排序
    sort.Slice(processes, func(i, j int) bool {
        return processes[i].Period < processes[j].Period
    })

    // 计算CPU利用率
    totalUtilization := 0.0
    for _, p := range processes {
        totalUtilization += float64(p.Execution) / float64(p.Period)
    }

    // 可调度性测试：U <= n*(2^(1/n) - 1)
    n := float64(len(processes))
    limit := n * (math.Pow(2, 1/n) - 1)

    return totalUtilization <= limit
}
```

## 进程间通信(IPC)

| IPC方式  | 适用场景                 | 速度  | 复杂度 | 可靠性 |
| -------- | ------------------------ | ----- | ------ | ------ |
| 管道     | 简单数据流，父子进程     | 中    | 低     | 中     |
| 消息队列 | 结构化数据，多进程通信   | 中    | 中     | 高     |
| 共享内存 | 高性能数据交换，本地进程 | 最快  | 高     | 需同步 |
| 信号量   | 同步控制                 | 快    | 高     | 高     |
| 套接字   | 网络通信，跨进程         | 中-高 | 高     | 高     |
| 信号     | 异步事件通知             | 最快  | 低     | 中     |

选择建议：

- 本地进程通信优先考虑共享内存+信号量（最快）
- 不同主机间通信必须使用网络套接字
- 需要流式数据传输，管道更合适
- 复杂结构化数据交换，消息队列更合适
- 简单通知机制，信号足够

### 匿名管道(Anonymous Pipe)

特点：单向通信，只能在具有亲缘关系的进程间使用（如父子进程）
实现：通过内核缓冲区实现
限制：数据只能单向流动，半双工通信

```go
// Go实现匿名管道示例
package main

import (
    "fmt"
    "os"
    "os/exec"
)

func main() {
    // 创建管道
    reader, writer, err := os.Pipe()
    if err != nil {
        fmt.Println("创建管道失败:", err)
        return
    }

    // 启动子进程
    cmd := exec.Command("wc", "-c")
    cmd.Stdin = reader
    output, _ := cmd.CombinedOutput()

    // 父进程写入数据到管道
    writer.Write([]byte("Hello, world!"))
    writer.Close()

    fmt.Printf("子进程输出: %s", output)
}
```

### 命名管道(Named Pipe / FIFO)

特点：可以在无关进程间使用，以文件系统路径形式存在
实现：通过特殊文件实现，通信双方通过读写路径相同的FIFO文件

```go
// Go实现命名管道示例
package main

import (
    "fmt"
    "os"
)

func main() {
    fifoPath := "/tmp/myfifo"

    // 创建命名管道
    os.Remove(fifoPath) // 先删除已存在的
    err := os.Mkfifo(fifoPath, 0666)
    if err != nil {
        fmt.Println("创建命名管道失败:", err)
        return
    }

    // 进程A - 写端
    go func() {
        writer, _ := os.OpenFile(fifoPath, os.O_WRONLY, 0)
        writer.WriteString("Hello from Named Pipe!")
        writer.Close()
    }()

    // 进程B - 读端
    reader, _ := os.OpenFile(fifoPath, os.O_RDONLY, 0)
    buf := make([]byte, 100)
    n, _ := reader.Read(buf)
    fmt.Printf("从管道读取: %s", buf[:n])
    reader.Close()
}
```

### 消息队列(Message Queue)

特点：消息的链表，保存在内核中，多个进程可以读写
优势：可以实现任意进程间通信，通信方式全双工
类型：System V消息队列和POSIX消息队列

```go
// Go实现消息队列示例（使用mq库）
package main

import (
    "fmt"
    "os"
    "syscall"
    "unsafe"
)

const (
    MQ_PATH = "/tmp/mymq"
    MQ_FLAG = syscall.O_CREAT | syscall.O_RDWR
    MQ_MODE = 0666
)

func main() {
    // 创建打开消息队列
    mqAttr := &syscall.MqAttr{
        MsgSize: 1024, // 消息大小
        MsgMax: 10,   // 最大消息数
    }

    mqd, err := syscall.MqOpen(MQ_PATH, MQ_FLAG, MQ_MODE, mqAttr)
    if err != nil {
        fmt.Println("消息队列创建失败:", err)
        return
    }
    defer syscall.MqUnlink(MQ_PATH)
    defer syscall.MqClose(mqd)

    // 发送消息
    msg := "Hello from Message Queue"
    _, err = syscall.MqSend(mqd, msg, len(msg), 0, 0)
    if err != nil {
        fmt.Println("发送消息失败:", err)
        return
    }

    // 接收消息
    buf := make([]byte, 1024)
    n, _, err := syscall.MqReceive(mqd, buf, 1024, 0, 0)
    if err != nil {
        fmt.Println("接收消息失败:", err)
        return
    }

    fmt.Printf("接收到消息: %s\n", buf[:n])
}
```

### 共享内存(Shared Memory)

特点：多个进程可以直接读写同一块内存空间，速度最快
优势：不需要数据从内核空间到用户空间的拷贝，效率最高
注意：需要同步机制（如信号量）来保证数据一致性

```go
// Go实现共享内存示例（mmap）
package main

import (
    "fmt"
    "os"
    "syscall"
    "unsafe"
)

const SHM_SIZE = 4096 // 4KB共享内存

func main() {
    shmPath := "/tmp/shmem"

    // 创建共享内存文件
    fd, err := os.Create(shmPath)
    if err != nil {
        fmt.Println("创建共享内存文件失败:", err)
        return
    }
    fd.Truncate(SHM_SIZE)
    fd.Close()

    // 打开文件并映射到内存
    fd, err = os.Open(shmPath, os.O_RDWR, 0)
    if err != nil {
        fmt.Println("打开共享内存文件失败:", err)
        return
    }
    defer fd.Close()

    // 将文件映射到内存
    data, err := syscall.Mmap(int(fd.Fd()), 0, SHM_SIZE,
        syscall.PROT_READ|syscPROT_WRITE, syscall.MAP_SHARED)
    if err != nil {
        fmt.Println("映射内存失败:", err)
        return
    }
    defer syscall.Munmap(data)

    // 写入数据
    msg := "共享内存内容"
    copy(data[:len(msg)], msg)

    // 读取数据
    fmt.Printf("读取共享内存内容: %s\n", string(data[:len(msg)]))
}
```

### 信号量(Semaphore)

- 特点：主要用于进程间同步，也可用于互斥访问资源
- 作用：控制多个进程对共享资源的访问
- 类型：二值信号量（互斥锁）和计数信号量（资源池）

```go
// Go实现信号量示例（使用第三方库github.com/fluent/posix_ipc)
package main

import (
    "fmt"
    "time"
    "github.com/fluent/posix_ipc"
)

func main() {
    // 创建或打开信号量
    sem, err := posix_ipc.NewSemaphore("example_semaphore",
        posix_ipc.O_CREAT, 0666, &posix_ipc.SemaphoreAttr{Value: 1})
    if err != nil {
        fmt.Println("创建信号量失败:", err)
        return
    }
    defer sem.Destroy()
    defer sem.Close()

    // 进程A - 获取信号量
    go func() {
        sem.Lock()
        fmt.Println("进程A获取信号量")
        defer sem.Unlock()
        time.Sleep(2 * time.Second)
        fmt.Println("进程A释放信号量")
    }()

    // 进程B - 尝试获取信号量
    time.Sleep(100 * time.Millisecond) // 确保A先执行
    sem.Lock()
    fmt.Println("进程B获取信号量")
    sem.Unlock()
}
```

### 套接字(Socket)

特点：最通用的IPC方式，可用于不同主机间的通信
类型：

- 网络套接字：TCP/IP、UDP协议
- 本地套接字(UNIX Domain Socket)：用于同一主机上的进程通信

```go
// TCP套接字通信示例 - 服务器端
package main

import (
    "fmt"
    "net"
)

func handleConnection(conn net.Conn) {
    defer conn.Close()

    buf := make([]byte, 1024)
    n, _ := conn.Read(buf)
    fmt.Printf("服务器收到: %s\n", string(buf[:n]))

    conn.Write([]byte("Hello from server!"))
}

func main() {
    listener, err := net.Listen("tcp", ":8080")
    if err != nil {
        fmt.Println("监听失败:", err)
        return
    }
    defer listener.Close()

    fmt.Println("服务器启动，监听端口8080...")

    for {
        conn, err := listener.Accept()
        if err != nil {
            fmt.Println("接受连接失败:", err)
            continue
        }

        go handleConnection(conn)
    }
}
```

### 信号(Signal)

特点：用于异步通知，用于处理特定事件
常用信号：

- SIGINT：中断进程
- SIGTERM：终止进程
- SIGUSR1/SIGUSR2：用户自定义信号

```go
// Go实现信号处理示例
package main

import (
    "fmt"
    "os"
    "os/signal"
    "syscall"
    "time"
)

func main() {
    // 创建信号通道
    signalChan := make(chan os.Signal, 1)
    signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGUSR1)

    go func() {
        for sig := range signalChan {
            switch sig {
            case syscall.SIGINT:
                fmt.Println("收到SIGINT信号，准备退出...")
                os.Exit(0)
            case syscall.SIGTERM:
                fmt.Println("收到SIGTERM信号，准备退出...")
                os.Exit(0)
            case syscall.SIGUSR1:
                fmt.Println("收到用户自定义信号SIGUSR1")
            default:
                fmt.Println("收到未知信号:", sig)
            }
        }
    }()

    fmt.Println("程序运行中，按Ctrl+C发送SIGINT信号...")
    for {
        time.Sleep(1 * time.Second)
        fmt.Println("程序正常运行...")
    }
}
```

### 文件(File)

进程可以通过读写文件进行通信，常用于进程之间的间接通信，例如使用临时文件或共享文件

## 进程监控与调试

### 监控工具

```bash
# 进程树视图
pstree -p

# 详细进程信息
ps auxf --forest

# 实时监控资源使用
top -p <pid> -b -n 1

# 内存映射分析
cat /proc/<pid>/maps

# 查看打开文件
lsof -p <pid>

# 跟踪系统调用
strace -fp <pid>
```

### 进程资源限制

使用cgroups限制进程资源：

```bash
# 创建cgroup
sudo mkdir /sys/fs/cgroup/cpu/mygroup
# 设置CPU限制
echo 50000 | sudo tee /sys/fs/cgroup/cpu/mygroup/cpu.cfs_quota_us
echo 100000 | sudo tee /sys/fs/cgroup/cpu/mygroup/cpu.cfs_period_us

# 启动受限进程
sudo -E stress --cpu 2

# 将进程加入cgroup
echo <pid> | sudo tee /sys/fs/cgroup/cpu/mygroup/tasks
```

# 线程

## 线程的本质

线程是程序执行的最小单位，是操作系统调度的基本实体。一个进程可以包含多个线程，它们共享进程的资源，同时拥有独立的执行路径。

- 轻量级进程：比进程更小的调度单位
- 资源共享：同一进程内的线程共享地址空间、文件描述符等
- 独立执行：每个线程有自己的程序计数器、寄存器和栈空间
- 并发执行：多个线程可交替执行

## 线程模型

### 主流线程模型

1:1模型（用户级线程：1个用户线程对应1个内核线程）

- 代表实现：Linux threads, Windows threads
- 优点：支持多核并行，可利用硬件并发
- 缺点：创建开销大，切换频繁

N:1模型（多个用户线程对应1个内核线程）

- 代表实现：早期Java线程（用户级线程库）
- 优点：切换快，实现简单
- 缺点：无法利用多核，阻塞会影响整个进程

M:N模型（M个用户线程对应N个内核线程）

- 代表实现：Go的goroutine, Java的ForkJoin
- 优点：平衡性能与资源使用
- 缺点：实现复杂，调度算法复杂

### Go语言的M:N实现

```go
// Go调度器模型概述
/*
G - Goroutine (用户线程)
M - OS Thread (内核线程)
P - Processor (处理器，包含调度上下文)
*/

// 创建多个goroutine
package main

import (
    "fmt"
    "runtime"
    "sync"
)

func worker(id int, wg *sync.WaitGroup) {
    defer wg.Done()
    fmt.Printf("Worker %d: started\n", id)
    // 模拟工作负载
    for i := 0; i < 1000; i++ {
        _ = i * i
    }
    fmt.Printf("Worker %d: finished\n", id)
}

func main() {
    var wg sync.WaitGroup
    numWorkers := 10
    runtime.GOMAXPROCS(4) // 设置使用4个逻辑核心

    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go worker(i, &wg)
    }

    wg.Wait()
    fmt.Println("All workers completed")
}
```

## 线程同步与通信

### 同步机制

互斥锁（Mutex）

```go
package main

import (
    "fmt"
    "sync"
)

type Counter struct {
    mu   sync.Mutex
    count int
}

func (c *Counter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.count++
}

func main() {
    counter := Counter{}
    var wg sync.WaitGroup
    var numGoroutines = 10
    var increments = 1000

    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < increments; j++ {
                counter.Increment()
            }
        }()
    }

    wg.Wait()
    fmt.Printf("Final count: %d (expected: %d)\n",
        counter.count, numGoroutines*increments)
}
```

读写锁（RWMutex）

```go
type SafeMap struct {
    mu    sync.RWMutex
    items map[string]string
}

func (sm *SafeMap) Read(key string) string {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    return sm.items[key]
}

func (sm *SafeMap) Write(key, value string) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.items[key] = value
}
```

条件变量（Cond）

```go
type TaskQueue struct {
    tasks    []string
    cond     *sync.Cond
    mu       sync.Mutex
}

func (tq *TaskQueue) AddTask(task string) {
    tq.mu.Lock()
    defer tq.mu.Unlock()
    tq.tasks = append(tq.tasks, task)
    tq.cond.Signal() // 唤醒一个等待的worker
}

func (tq *TaskQueue) GetTask() string {
    tq.mu.Lock()
    defer tq.mu.Unlock()

    for len(tq.tasks) == 0 {
        tq.cond.Wait() // 等待任务到达
    }

    task := tq.tasks[0]
    tq.tasks = tq.tasks[1:]
    return task
}
```

### 通信机制

通道（Channel）

```go
// 生产者-消费者模式
package main

import (
    "fmt"
    "time"
)

func producer(id int, ch chan<- int) {
    for i := 0; i < 5; i++ {
        value := id * 10 + i
        fmt.Printf("Producer %d: sending %d\n", id, value)
        ch <- value
        time.Sleep(100 * time.Millisecond)
    }
    fmt.Printf("Producer %d: done\n", id)
}

func consumer(id int, ch <-chan int) {
    for value := range ch {
        fmt.Printf("Consumer %d: received %d\n", id, value)
        time.Sleep(150 * time.Millisecond)
    }
    fmt.Printf("Consumer %d: done\n", id)
}

func main() {
    ch := make(chan int, 5)

    go producer(1, ch)
    go producer(2, ch)
    go consumer(1, ch)

    time.Sleep(2 * time.Second)
    close(ch)

    // 等待消费者处理完
    time.Sleep(1 * time.Second)
}
```

选择器（Select）

```go
func selectExample() {
    ch1 := make(chan int)
    ch2 := make(chan string)

    go func() {
        time.Sleep(100 * time.Millisecond)
        ch1 <- 42
    }()

    go func() {
        time.Sleep(150 * time.Millisecond)
        ch2 <- "Hello"
    }()

    for i := 0; i < 2; i++ {
        select {
        case num := <-ch1:
            fmt.Printf("Received from ch1: %d\n", num)
        case str := <-ch2:
            fmt.Printf("Received from ch2: %s\n", str)
        case <-time.After(200 * time.Millisecond):
            fmt.Println("Timeout occurred")
        }
    }
}
```

## 生命周期与状态

### 线程状态

```
创建态 → 就绪态 → 运行态 → 阻塞态 → 终止态
   ↓        ↑      ↓       ↑       ↓
 [内核]   [调度]  [IO]   [等待]   [回收]
```

### 状态转换

```go
// Go中的goroutine状态
/*
 1. 创建: go func() 创建goroutine
 2. 就绪: 进入调度器队列
 3. 运行: 被调度器分配到M上执行
 4. 阻塞: 系统调用、channel操作、锁等待等
 5. 终止: 函数执行完成或panic恢复
*/
```

## 多线程编程的挑战

### 死锁

```go
// 死锁示例
type DeadlockExample struct {
    mu1, mu2 sync.Mutex
}

func (de *DeadlockExample) deadlockMethod1() {
    de.mu1.Lock()
    defer de.mu1.Unlock()

    // 尝试获取de.mu2
    de.mu2.Lock()
    defer de.mu2.Unlock()
}

func (de *DeadlockExample) deadlockMethod2() {
    de.mu2.Lock()
    defer de.mu2.Unlock()

    // 尝试获取de.mu1
    de.mu1.Lock()
    defer de.mu1.Unlock()
}

func main() {
    de := &DeadlockExample{}
    go de.deadlockMethod1()
    de.deadlockMethod2() // 死锁
}
```

死锁预防：

1. 按固定顺序获取锁
1. 使用带超时的锁
1. 避免嵌套锁
1. 死锁检测算法

### 竞态条件

```go
// 竞态条件示例
func raceConditionExample() {
    counter := 0
    var wg sync.WaitGroup

    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter++ // 非原子操作
        }()
    }

    wg.Wait()
    fmt.Printf("Counter: %d (expected: 1000)\n", counter)
}
```

竞态条件解决：

1. 使用互斥锁保护共享资源
1. 使用原子操作
1. 使用不可变数据结构

### 线程泄漏

```go
// 线程泄漏示例
func threadLeakExample() {
    for i := 0; i < 100000; i++ {
        go func(id int) {
            time.Sleep(1 * time.Hour)
            // 没有正确处理退出条件
        }(i)
    }
}
```

预防措施：

1. 使用工作池模式
1. 设置超时机制
1. 资源监控和告警

## 线程池模型与最佳实践

### 线程池实现

```go
// 固定大小线程池实现
package main

import (
    "fmt"
    "sync"
    "time"
)

type WorkerPool struct {
    tasks    chan func()
    wg       sync.WaitGroup
    stop     chan struct{}
}

func NewWorkerPool(numWorkers int) *WorkerPool {
    pool := &WorkerPool{
        tasks: make(chan func(), 100),
        stop:  make(chan struct{}),
    }

    // 创建worker
    for i := 0; i < numWorkers; i++ {
        pool.wg.Add(1)
        go pool.worker(i)
    }

    return pool
}

func (p *WorkerPool) worker(id int) {
    defer p.wg.Done()
    for {
        select {
        case task := <-p.tasks:
            task()
        case <-p.stop:
            fmt.Printf("Worker %d stopping\n", id)
            return
        }
    }
}

func (p *WorkerPool) Submit(task func()) {
    select {
    case p.tasks <- task:
    default:
        fmt.Println("Task queue full, rejecting task")
    }
}

func (p *WorkerPool) Shutdown() {
    close(p.stop)
    p.wg.Wait()
}

func main() {
    pool := NewWorkerPool(3)

    // 提交任务
    for i := 0; i < 10; i++ {
        id := i
        pool.Submit(func() {
            fmt.Printf("Processing task %d\n", id)
            time.Sleep(100 * time.Millisecond)
        })
    }

    // 关闭线程池
    pool.Shutdown()
}
```

### 工作队列模式

```go
// 工作队列实现
type Task struct {
    ID      int
    Data    interface{}
    Handler func(interface{}) interface{}
}

type Worker struct {
    ID      int
    Tasks   chan *Task
    Results chan *Task
    Quit    chan struct{}
}

func NewWorker(id int, tasks chan *Task, results chan *Task) *Worker {
    return &Worker{
        ID:      id,
        Tasks:   tasks,
        Results: results,
        Quit:    make(chan struct{}),
    }
}

func (w *Worker) Start() {
    go func() {
        for {
            select {
            case task := <-w.Tasks:
                result := task.Handler(task.Data)
                task.Data = result
                w.Results <- task
            case <-w.Quit:
                return
            }
        }
    }()
}

func main() {
    numWorkers := 4
    tasks := make(chan *Task, 100)
    results := make(chan *Task, 100)

    // 创建workers
    for i := 0; i < numWorkers; i++ {
        worker := NewWorker(i, tasks, results)
        worker.Start()
    }

    // 提交任务
    for i := 0; i < 20; i++ {
        task := &Task{
            ID:    i,
            Data:  i,
            Handler: func(data interface{}) interface{} {
                n := data.(int)
                return n * 2
            },
        }
        tasks <- task
    }

    // 收集结果
    for i := 0; i < 20; i++ {
        task := <-results
        fmt.Printf("Result for task %d: %v\n", task.ID, task.Data)
    }

    // 关闭channels
    close(tasks)
    close(results)
}
```

## 线程安全设计模式

### 不可变模式

```go
// 不可变数据结构
type ImmutableCounter struct {
    value int
}

func (ic ImmutableCounter) Increment() ImmutableCounter {
    return ImmutableCounter{value: ic.value + 1}
}

func (ic ImmutableCounter) Value() int {
    return ic.value
}

// 使用示例
func immutableExample() {
    counter := ImmutableCounter{value: 0}

    var wg sync.WaitGroup
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(idx int) {
            defer wg.Done()
            // 每次创建新的不可变对象
            counter = counter.Increment()
            log.Printf("Worker %d: %d", idx, counter.Value())
        }(i)
    }

    wg.Wait()
    log.Printf("Final count: %d", counter.Value())
}
```

### 线程局部存储

```go
// 使用context实现线程局部存储
func main() {
    ctx := context.Background()

    // 在context中存储数据
    ctx = context.WithValue(ctx, "userID", 123)
    ctx = context.WithValue(ctx, "sessionID", "abc123")

    // 在不同goroutine间传递context
    go func(ctx context.Context) {
        // 从context中获取数据
        userID := ctx.Value("userID").(int)
        sessionID := ctx.Value("sessionID").(string)

        log.Printf("Worker userID: %d, sessionID: %s", userID, sessionID)
    }(ctx)
}
```

### 消息传递模式

```go
// 使用通道实现生产者-消费者模式
type Message struct {
    Type    string
    Payload interface{}
}

type MessageBus struct {
    subscribers map[string][]chan Message
    mu          sync.RWMutex
}

func NewMessageBus() *MessageBus {
    return &MessageBus{
        subscribers: make(map[string][]chan Message),
    }
}

func (mb *MessageBus) Subscribe(topic string) chan Message {
    mb.mu.Lock()
    defer mb.mu.Unlock()

    ch := make(chan Message, 100)
    mb.subscribers[topic] = append(mb.subscribers[topic], ch)
    return ch
}

func (mb *MessageBus) Publish(topic string, msg Message) {
    mb.mu.RLock()
    defer mb.mu.RUnlock()

    for _, ch := range mb.subscribers[topic] {
        select {
        case ch <- msg:
        default:
            log.Printf("Subscriber channel full for topic %s", topic)
        }
    }
}

// 使用示例
func messageBusExample() {
    bus := NewMessageBus()

    // 订阅者
    sub1 := bus.Subscribe("topic1")
    sub2 := bus.Subscribe("topic2")

    // 发布者
    go func() {
        bus.Publish("topic1", Message{Type: "event", Payload: "Hello"})
        bus.Publish("topic2", Message{Type: "event", Payload: "World"})
    }()

    // 处理消息
    go func() {
        for msg := range sub1 {
            log.Printf("Subscriber 1: %v", msg)
        }
    }()

    go func() {
        for msg := range sub2 {
            log.Printf("Subscriber 2: %v", msg)
        }
    }()

    time.Sleep(1 * time.Second)
}
```

# 进程 V.S. 线程

## 层次结构

```
进程 (PID)
├── 线程1 (TID1)
│   ├── 栈空间
│   ├── 寄存器状态
│   └── 程序计数器
├── 线程2 (TID2)
│   ├── 栈空间
│   ├── 寄存器状态
│   └── 程序计数器
└── 共享资源
    ├── 代码段
    ├── 数据段
    ├── 文件描述符表
    └── 堆内存
```

## 对比分析

| 特性 | 进程                           | 线程                 |
| ---- | ------------------------------ | -------------------- |
| 本质 | 资源分配单位                   | CPU调度单位          |
| 资源 | 独立地址空间                   | 共享进程资源         |
| 开销 | 大（创建、销毁、切换）内存独立 | 小 共享内存          |
| 切换 | 大                             | 小                   |
| 通信 | 复杂（IPC机制）                | 简单（共享内存）     |
| 隔离 | 高（完全隔离）                 | 低（部分隔离）       |
| 并发 | 进程级并发                     | 线程级并发           |
| 调度 | 操作系统调度                   | 操作系统或用户态调度 |
| 故障 | 独立，不影响其他进程           | 可能影响整个进程     |

## 关键差异

### 内存空间对比

| 特性     | 进程                      | 线程                          |
| -------- | ------------------------- | ----------------------------- |
| 地址空间 | 独立地址空间              | 共享进程地址空间              |
| 内存分布 | 完全隔离                  | 线程私有栈 + 共享/读数据段    |
| 创建开销 | 大（复制页表等）          | 小（只分配栈和TSS）           |
| 切换开销 | 大（保存/恢复完整上下文） | 小（只需保存/恢复少量寄存器） |

### 性能与资源对比

| 特性      | 进程               | 线程                   |
| --------- | ------------------ | ---------------------- |
| 内存消耗  | 高(独立地址空间)   | 低(共享内存)           |
| CPU利用率 | 中等(需要更多调度) | 高(切换成本低)         |
| 启动时间  | 长                 | 短                     |
| 扩展性    | 可在不同机器间扩展 | 通常在同一进程内       |
| 故障恢复  | 简单(重启进程)     | 复杂(可能影响整个进程) |

### 调度策略差异

| 特性         | 进程调度           | 线程调度         |
| ------------ | ------------------ | ---------------- |
| 调度单位     | 进程               | 线程             |
| 处理器亲和性 | 整个进程共享       | 可单独设置       |
| 线程优先级   | 通过进程nice值控制 | 可单独设置优先级 |
| 调度延迟     | 相对较长           | 相对较短         |

### 并发模型对比

| 特性       | 进程模型             | 线程模型                 |
| ---------- | -------------------- | ------------------------ |
| 并发单位   | 进程级别             | 线程级别                 |
| 并行能力   | 多进程可利用多核     | 多线程可利用多核         |
| 资源开销   | 高，每个进程独立内存 | 低，线程共享内存         |
| 故障隔离   | 高，进程间故障不传播 | 低，线程崩溃影响整个进程 |
| 编程复杂度 | 简单，无需同步原语   | 复杂，需要锁、同步等机制 |

### 选型原则对比

计算密集型 vs I/O密集型

- 计算密集型：进程模型更合适
- I/O密集型：线程模型更合适

安全性与隔离需求

- 高安全需求：进程模型
- 内部协作：线程模型

扩展性与分布性需求

- 需要跨机器扩展：进程模型
- 同一机器内高并发：线程模型

故障恢复需求

- 快速独立恢复：进程模型
- 故障容忍：线程模型+隔离

| 场景           | 推荐模型 | 理由                   |
| -------------- | -------- | ---------------------- |
| Web服务器      | 线程模型 | 大量并发连接，共享资源 |
| 数据处理       | 混合模型 | 分阶段处理，并行计算   |
| 微服务架构     | 进程模型 | 独立部署，故障隔离     |
| 实时游戏       | 进程模型 | 稳定性要求高           |
| 高频交易       | 线程模型 | 低延迟要求             |
| 长时间运行任务 | 混合模型 | 资源隔离+并行处理      |

优先考虑线程模型当：

- 需要高并发处理
- 任务间需要频繁通信
- 资源效率是关键因素
- 同一进程内的协作场景

优先考虑进程模型当：

- 安全和隔离是首要考虑
- 需要独立扩展和部署
- 处理计算密集型任务
- 微服务架构设计

混合模型适合：

- 需要平衡隔离性与性能
- 分阶段处理流程
- 复杂系统架构设计
