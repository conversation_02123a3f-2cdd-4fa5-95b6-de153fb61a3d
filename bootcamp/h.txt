命令用法: md-fmt [选项] <Markdown 文件名>

定制风格
     --flavor-hugging-face-wechat 使用 Hugging Face 微信公众号风格来调整输入内容


标准选项
-h | --help                       输出当前的帮助信息 (--help 更详细)


格式化选项
     --colon                      中文冒号转为英文冒号+空格
                                  比如: “：” => “: ”。如果在行末，则会生成“:”，即删除末尾的空格。

     --enclose-table              表格的每一行两端使用 |
                                  比如表格的对齐行: “:-- | :--” => “| :-- | :-- |”。

     --extract-links              从段落中提取超链接
                                  比如:
                                  “这句话包含了一个[超链接](https://dongs.xyz/)” =>
                                  “这句话包含了一个[超链接](https://dongs.xyz/)\n\n超链接:\n<url>https://dongs.xyz/</url>”

     --extract-no-anchor          提取链接时不包括锚点部分

     --google-list-style          使用 Google 风格的列表样式

     --italic-to-bold             斜体字更改为加粗文字
                                  比如: “a*b*c” => “a**b**c”

     --parenthesis                中文括号转为英文括号+空格
                                  比如:
                                  “（）” => “ () ”
                                  最终结果将根据上下文去除多余的空格。

     --pretty-table               以相同列宽展示表格中的每一列

     --semicolon                  中文分号转为英文分号+空格
                                  比如: “；” => “; ”
                                  最终结果将根据上下文去除多余的空格。

     --strip-enclosed-url         去除 <url> 内部的非必要空格
                                  比如: “<url> https://dongs.xyz/ </url>” => “<url>https://dongs.xyz/</url>”

     --whitespace-around-bold     加粗文字前后使用空格
                                  比如:
                                  “a**b**c” => “a **b** c”;
                                  “a **b**c” => “a **b** c”。
                                  最终结果将根据上下文去除多余的空格。


调试选项
     --debug-markdown-tag-pair    执行 Markdown 模块的函数时输出成对的 Tag (仅适用于 `debug` feature)


附加选项
     --print                      在控制台输出当前启用的调整

     --print-compact              窄行距输出当前启用的调整

