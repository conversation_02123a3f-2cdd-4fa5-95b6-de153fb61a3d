# 数据类型

Redis 共提供 10 种 原生数据类型，最常用的是前 5 种（面试高频）。下面按「使用频率 → 特点 → 典型命令 → 场景示例」四要素一次讲清，方便速记与查表。

## 常见

### String（字符串）

| 特点               | 性质                          | 命令                    | 场景示例                                               |
| ------------------ | ----------------------------- | ----------------------- | ------------------------------------------------------ |
| 最基本，二进制安全 | 可存任何类型，最大长度 512 MB | SET / GET / INCR / DECR | 计数器、缓存、分布式锁（带 NX/EX 选项）、分布式session |

#### Redis Simple Dynamic String (SDS)

Redis的简单动态字符串（SDS）是Redis自研的字符串实现，相比C语言原生字符串具有显著优势

##### 结构定义

```c
struct sdshdr {
    int len;        // 已使用长度
    int free;       // 剩余可用空间
    char buf[];     // 数据缓冲区
};
```

内存布局示例

```
-----------------------------------------
| len(4) | free(4) | buf[n]             |
-----------------------------------------
| 5      | 3       | 'h','e','l','l','o','\0',' ',' ' |
-----------------------------------------
```

##### 核心优势

1. 常数时间获取长度

C字符串：需遍历计算长度 O(n)
SDS：直接读取len字段 O(1)

2. 杜绝缓冲区溢出

C字符串：修改时需要手动检查空间
SDS：自动检查并扩展空间

3. 减少内存重分配

采用空间预分配和惰性空间释放策略

##### 与C字符串对比

| 特性       | C字符串       | SDS          |
| ---------- | ------------- | ------------ |
| 长度获取   | O(n)遍历      | O(1)直接读取 |
| 内存安全   | 手动管理      | 自动管理     |
| 缓冲区溢出 | 容易发生      | 自动扩展     |
| 二进制安全 | 否('\\0'截断) | 是           |
| 内存重分配 | 频繁          | 减少         |
| 空间复杂度 | 紧凑          | 额外元数据   |

##### 编码方式

###### int 编码

适用场景：值可以表示为整数的字符串（如 `"123"`、`"-9999"`）

内存结构：

```c
// 存储实际整数值而非字符串
9999  // 直接存储为带符号整数
```

优点：

- 极致内存节约（仅需 8 字节存储 64 位整数）
- 整数操作快速（加减乘除等）

转换规则：

- 字符串 → 整数：使用 `isInteger()` 判断是否可解析为整数
- 整数 → 字符串：当值被修改或长度超过 20 位（REDIS_LONG_MAX 的字符串表示）时转换

示例：

```redis
SET counter 1000      # 存储为 int 编码
INCR counter         # O(1) 整数运算
GET counter          # 返回 "1001"（仍为 int）
SET counter "hello"  # 自动转为 raw 编码
```

###### embstr 编码

适用场景：短字符串（通常 ≤ 44 字节）

内存结构：

```c
// SDS head + data 连续分配
+-------------------------+
| SDS hdr (3/5 bytes) | data | '\0'
+-------------------------+
```

特点：

- 单次内存分配：头部和内容连续存储
- 只读友好：适合读取操作多的场景
- 分配更快：比 raw 少一次内存分配

限制：

- 修改时通常需要先转为 raw 编码
- 大小有限制（44 字节是 Redis 4.0 默认值）

示例：

```redis
SET user:1000 "Alice"    # 存储为 embstr
GET user:1000           # O(1) 快速读取
APPEND user:1000 " Smith"  # 转换为 raw（长度超过限制）
```

###### raw 编码

适用场景：

- 长字符串（> 44 字节）
- 需要频繁修改的字符串（即使是短字符串）

内存结构：

```c
// SDS head + data 分离分配
+------------+  +-------------------------+
|   Raw obj  |->| SDS hdr + data | '\0'   |
+------------+  +-------------------------+
```

特点：

- 高效修改：预留 free 空间避免频繁重分配
- 空间预分配：修改时按指数增长预留空间
- 惰性释放：缩短时暂不释放内存

修改性能：

```c
// 预分配示例：当前长度 50，追加 100 字节
// 新分配 = 50 + 100 + 预分配空间
// 预分配空间 = min(new_len, 1MB) 或 new_len * 2
```

##### 转换规则

| 操作            | int → embstr | int → raw   | embstr → raw | raw → embstr |
| --------------- | ------------ | ----------- | ------------ | ------------ |
| SET (新值)      | ❌ 直接 int  | ❌ 直接 raw | ❌ 直接 raw  | ❌ 直接 raw  |
| 整数运算 (INCR) | ✅ 维持 int  | ❌ 不会发生 | ❌ 不会发生  | ❌ 不会发生  |
| 字符串修改      | ➡️ 转 raw    | ➡️ 扩展     | ➡️ 分配新块  | ✅ 基于修改  |
| 长度超过限制    | ➡️ 转 raw    | ✅ 维持 raw | ➡️ 改为 raw  | ❌ 不会变    |

> 关键注意：一旦转为 raw，即使长度缩短也不会自动降级回 embstr，避免频繁转换开销

##### 性能对比

| 编码方式 | 内存占用  | 读取速度 | 写入速度 | 适用场景                |
| -------- | --------- | -------- | -------- | ----------------------- |
| int      | 极低 (8B) | ⚡ 最快  | ⚡ 最快  | 计数器、数值缓存        |
| embstr   | 极低      | ⚡ 很快  | 👎 慢    | 短字符串只读/少修改场景 |
| raw      | 较高      | ⚡ 快    | ⚡ 很快  | 长字符串/频繁修改场景   |

##### 应用建议

1. 整数优化

```redis
# ✅ 高效整数操作
INCR session:1001
INCRBY views:article:10 50
# ❌ 避免字符串形式存储计数器
SET visits "123"  # 会浪费 int 编码优势
```

2. 字符串长度控制

```redis
# 短字符串使用 embstr（≤44字节）
SET session:user:1001 "ABCDEF123..."
# 长内容使用 raw
SET article:10 "Lorem ipsum dolor sit amet..."
```

3. 修改频率考虑

```redis
# 频繁修改用 raw
PUSH logs "123"        # 转为 raw 后追加更高效
# 纯查询用 embstr/int
SELECT user:name:1001  # 优化为 embstr/int
```

### Hash（哈希表）

| 特点                     | 性质                              | 命令                  | 场景示例                                        |
| ------------------------ | --------------------------------- | --------------------- | ----------------------------------------------- |
| field-value 对，适合对象 | 内部使用哈希表实现 适合小规模数据 | HSET / HGET / HINCRBY | 缓存对象、购物车、用户档案、商品属性、用户级 PV |

### List（列表）

| 特点                  | 性质         | 命令                  | 场景示例                     |
| --------------------- | ------------ | --------------------- | ---------------------------- |
| 双向链表，可头/尾插入 | 底层双向链表 | LPUSH / RPOP / LRANGE | 阻塞队列、消息队列、历史记录 |

### Set（无序集合）

| 特点                       | 性质       | 命令                     | 场景示例                                        |
| -------------------------- | ---------- | ------------------------ | ----------------------------------------------- |
| 快速查找、去重、交并差运算 | 哈希表实现 | SADD / SMEMBERS / SINTER | 标签系统、唯一用户集合、共同好友、点赞/收藏去重 |

### ZSet（有序集合 / Sorted Set）

| 特点                      | 性质                         | 命令                    | 场景示例                                                              |
| ------------------------- | ---------------------------- | ----------------------- | --------------------------------------------------------------------- |
| 带 score 排序，可范围查询 | 跳表实现，支持快速的范围查询 | ZADD / ZINCRBY / ZRANGE | 任务调度、排行榜、延时队列（score=时间戳）、社交网络的关注/粉丝数排序 |

数据元素少时，用压缩列表Zip List节省内存

- 元素个数 \<= zset-max-ziplist-entries (default 128)
- 元素成员名和分值长度 \<= zset-max-ziplist-value (default 64bytes)

上述任何一个条件不满足，ZSet用 跳表+哈希表

- 跳表（Skip List）：存储数据的排序和快速查找
- 哈希表（Hash Table）：存储成员与其分数的映射提供快速查找

#### 跳表

跳表用“抛硬币”造出多级索引，把链表变成“概率版平衡树”，“以概率换平衡”的多层有序链表
多层索引的链表，每一层索引的元素在最底层的链表中可以找到的元素
Redis、LevelDB、Lucene 等都在用，因为它比平衡树代码短、范围遍历快、并发友好

##### 结构

![SkipList](https://raw.githubusercontent.com/UlricYang/FigureBed/main/img/20250808113434440.png)

- level 0 是完整的有序链表。
- 每往上一层，节点数以 概率 p（Redis 取 25%）随机保留，形成“快速通道”，平均层高 1.33，内存省
- 平均 O(log N) 层高，查找、插入、删除都是 O(log N)，最大层数 32
- span字段 → 快速计算rank（ZREVRANK 只需累加 span）
- backward指针 → 支持逆序ZREVRANGE无需回溯

##### 核心操作

###### 查找

1. 从顶层最大 forward 开始，向右 & 向下 逼近
1. 只需 log(N) 步即可定位节点（平均O(logN),最差O(n)）

例：找 score=50
从最上面的10开始 -> 跳到40（目标比40大） -> 对比下一个70（比目标小）-> 去下一层查找（下层40的next就是50）-> 找到

###### 插入

1. 随机掷硬币决定层高（每一个节点能否增加一层的概率是25%）
1. 在每一层找到插入位置，修改指针 & span 即可
   1.Redis保证插入/删除是原子操作（单线程，无锁）

例：插入48
定位第一个比score大的数据（上文查找过程找到50）-> 当前创建数据or增加一个层级（随机选择）-> 定位层级后插入目标元素（普通列表插入）

###### 删除

与插入对称，修改指针 & span，再释放节点

##### 回退指针（前去指针）

回退指针主要为了提高跳表的操作效率和灵活性，尤其是在频繁插入和删除的场景中减少节点之间指针的更新复杂度提升性能

例：删除节点
前驱指针 -> 一次遍历中找到并记录所有关联的前驱节点 -> 无需在变更指针时再次查找前驱节点

##### 复杂度与平衡树对比

| 指标       | 跳表                 | 平衡树                    |
| ---------- | -------------------- | ------------------------- |
| 查找       | O(log N)             | O(log N)                  |
| 插入/删除  | O(log N)             | O(log N)                  |
| 范围遍历   | 链表顺序扫描，非常快 | 需中序遍历，cache miss 高 |
| 代码复杂度 | 200 行搞定           | 旋转、颜色修复难维护      |
| 并发       | 链表好做 lock-free   | 树结构调整锁粒度大        |

#### 哈希表

Redis的Hash是一种键值对集合，可以将多个字段和值存储在同一个键中，便于管理关联数据

- 适合存储小数据，在内存中高效存储和操作
- 适合存储对象的属性，支持快速字段操作（增删改查）

##### 底层实现

一个Hash可以存 2^32 - 1 个键值对

-Redis6 之前，底层是 hashtable + ziplist
-Redis7 之后，底层是 hashtable + listpack

```
//这二者查找key效率都是O(n)，listpack解决了ziplist的级联更新问题
dstype = ziplist OR listpack
IF value(hash) < hash-max-${dstype}-entries AND hash-max-${dstype}-value
    USE ${dstype}
ELSE
    //用了hashtable则不会退回ziplist或listpack
    USE hashtable
```

##### Hashtable

Hashtable就是哈希表实现，查询O(1)

![HashTable](https://raw.githubusercontent.com/UlricYang/FigureBed/main/img/20250808124609915.png)

```c
typedef struct dictht {
    //哈希表数组
    dictEntry table;
    //哈希表大小
    unsigned long size;
    //哈希表大小掩码，用于计算索引值
    unsigned long sizemask;
    //该哈希表已有的节点数量
    unsigned long used;
} dictht;

typedef struct dictEntry {
    //键值对中的键
    void *key;

    //键值对中的值
    union {
        void *val;
        uint64_t u64;
        int64_t s64;
        double d;
    } v;
    //指向下一个哈希表节点，形成链表
    struct dictEntry *next;
} dictEntry;

```

哈希节点中value由联合体组成 -> 当值是整数或者浮点数时可以将值存在哈希节点 -> 不再需要使用一个指针指向实际值 -> 节省空间

##### 渐进式rehash

![rehash](https://raw.githubusercontent.com/UlricYang/FigureBed/main/img/20250808125356978.png)

插入数据时，所有数据写入哈希表1（ht[0]），哈希表2（ht[1]）此时就是一张没有分配空间的空表
随着数据越来越多，触发扩容条件

##### 扩容

```
负载因子 = 哈希表已保存节点数量 / 哈希表大小
IF 负载因子 > 5
    rehash
IF 负载因子 > 1
    IF NOT 持久化机制（RDB快照 or AOF重写）
        rehash
IF 负载因子 < 0.1
    IF NOT 持久化机制（RDB快照 or AOF重写）
        缩容（申请 ht[1]，大小 = 2^n ~ nearest(2×used) ）
```

| 步骤     | 动作                             | 耗时 |
| -------- | -------------------------------- | ---- |
| 建表     | 申请 ht[1]，大小 = 2^n ≥ 2×used  | O(1) |
| 双表并存 | rehashidx = 0                    | O(1) |
| 微步迁移 | 每条增删查改命令 顺带搬 1 个桶   | O(1) |
| 完结     | ht[0] 变空 → 交换指针 → 释放旧表 | O(1) |

#### 操作

##### 增删改

1. 哈希表存储成员和粉丝的映射，分数是哈希表中的值
1. 成员和分数插入跳表，跳表根据分数排序
1. 如果是更新，在哈希表更新成员分数并在跳表中改该成员的位置
1. 如果是删除，在哈希表删除映射，在跳表中删除该成员

| 命令              | 作用                | 复杂度   | 示例                       |
| ----------------- | ------------------- | -------- | -------------------------- |
| ZADD              | 添加/更新成员与分数 | O(log N) | ZADD rank 100 alice 90 bob |
| ZREM              | 删除指定成员        | O(log N) | ZREM rank bob              |
| ZINCRBY           | 原子增减分数        | O(log N) | ZINCRBY rank 5 alice       |
| ZPOPMIN / ZPOPMAX | 弹出最低/最高分     | O(log N) | ZPOPMIN rank 3             |

##### 查单条

在哈希表中查找成员分数

| 命令             | 作用          | 复杂度   | 示例              |
| ---------------- | ------------- | -------- | ----------------- |
| ZSCORE           | 取成员分数    | O(1)     | ZSCORE rank alice |
| ZRANK / ZREVRANK | 取正/倒序排名 | O(log N) | ZRANK rank alice  |
| ZCARD            | 集合元素总数  | O(1)     | ZCARD rank        |

##### 范围查询

| 命令             | 作用         | 复杂度       | 示例                                 |
| ---------------- | ------------ | ------------ | ------------------------------------ |
| ZRANGE           | 按排名区间   | O(log N + M) | ZRANGE rank 0 9 WITHSCORES           |
| ZREVRANGE        | 倒序排名区间 | O(log N + M) | ZREVRANGE rank 0 9                   |
| ZRANGEBYSCORE    | 按分数区间   | O(log N + M) | ZRANGEBYSCORE rank 80 100 LIMIT 0 10 |
| ZREVRANGEBYSCORE | 倒序分数区间 | O(log N + M) | ZREVRANGEBYSCORE rank 100 80         |
| ZCOUNT           | 分数区间计数 | O(log N)     | ZCOUNT rank 80 100                   |

##### 批量删除/交集/并集

| 命令             | 作用           | 复杂度       | 示例                          |
| ---------------- | -------------- | ------------ | ----------------------------- |
| ZREMRANGEBYRANK  | 按排名区间删除 | O(log N + M) | ZREMRANGEBYRANK rank 0 2      |
| ZREMRANGEBYSCORE | 按分数区间删除 | O(log N + M) | ZREMRANGEBYSCORE rank 0 60    |
| ZINTERSTORE      | 交集并存新key  | O(N\*log N)  | ZINTERSTORE out 2 rank1 rank2 |
| ZUNIONSTORE      | 并集并存新key  | O(N\*log N)  | ZUNIONSTORE out 2 rank1 rank2 |

##### 实战小结

- 排行榜：ZADD 更新分数 → ZREVRANGE 取 Top N
- 延时队列：score = 时间戳 → ZRANGEBYSCORE 0 now 拉取到期任务
- 滑动窗口限流：ZADD + ZREMRANGEBYSCORE + ZCARD 计数
- 社交网络关注/分数数排序：ZINCRBY 实时更新 + ZREVRANGE 排行榜

## 进阶

| 类型        | 特点                                      | 性质                       | 命令               | 场景                                       |
| ----------- | ----------------------------------------- | -------------------------- | ------------------ | ------------------------------------------ |
| Bitmap      | 以位为单位高效存储，使用空间少且操作快速  | 适合布尔值                 | SETBIT / BITCOUNT  | 亿级用户签到、用户登陆状态判断、是否访问过 |
| BitField    | redis字符串看作是一个由二进制位组成的数组 | 直接按位级随机读写         | BITFIELD           | 位级压缩存储（状态位、计数器）             |
| HyperLogLog | 概率性数据结构，内存占用固定              | 适合大规模数据的去重和计数 | PFADD / PFCOUNT    | 近似去重（PV / UV）、主要估算基数          |
| Geo         | 存储经纬度信息，支持空间查询              | 用于存储地理位置信息       | GEOADD / GEORADIUS | 附近的人、地图（坐标距离计算）             |
| Stream      | 日志数据结构，                            | 适合时间序列数据或消息流   | XADD / XREADGROUP  | 消息队列（Kafka-like 消息流，消费者组）    |

## 选型速查表

| 需求      | 首选类型      |
| --------- | ------------- |
| 页面级 PV | String (INCR) |
| 用户级 PV | Hash          |
| 排行榜    | ZSET          |
| 去重 UV   | HyperLogLog   |
| 用户签到  | Bitmap        |
| 消息队列  | List / Stream |
| 附近的人  | Geo           |

# Redis的快

纯内存 + 单线程事件循环 + 高效数据结构 + 零拷贝网络 + 精心编码

把“所有慢动作”全部砍掉：磁盘→内存、锁→单线程、通用结构→专用算法、多次拷贝→零拷贝，所以能在毫秒以内完成绝大多数请求。

![redis1.jpg](https://raw.githubusercontent.com/UlricYang/FigureBed/main/img/20250807140830017.jpg)

## 纯内存操作：纳秒级延迟

- 所有读写落在 主内存，无磁盘 I/O 随机寻道（ns vs ms）。
- 辅以 CPU Cache 亲和性：数据紧凑、预取命中高。

| 存储类型 | Register | L1 Cache | L2 Cache | L3 Cache | RAM    | SSD       | HDD     |
| -------- | -------- | -------- | -------- | -------- | ------ | --------- | ------- |
| 访问速度 | 0.3 ns   | 0.9 ns   | 2.8 ns   | 12.9 ns  | 120 ns | 50~150 ns | 1~10 ms |

## 单线程事件循环（Reactor 模式）

### 线程模型

Redis单线程主要指的是Redis网络IO和键值对读写操作是由一个线程完成的，持久化、集群等机制其实有后台线程完成

Redis把最核心的命令执行做成单线程是为了把 CPU 从“锁、上下文切换、缓存失效”这些浪费时间的动作里彻底解放出来，用“顺序无锁“换”极限低延迟“，一个线程完成“接收 → 解析 → 执行 → 回复”全部步骤

#### 单线程的够用

- 纯内存操作耗时 -> 几百纳秒～几微秒，CPU 根本跑不满
- 代码简单 -> 减少了线程上下文切换带来的性能开销
- 网络IO通过epoll/kqueue异步完成，阻塞点被事件循环接管 -> 单核10w+ QPS已远超一般业务需求

#### 单线程的优势

| 问题           | 单线程的解决方案             | 带来的收益               |
| -------------- | ---------------------------- | ------------------------ |
| CPU 上下文切换 | 只有一个线程在跑，零切换     | 微秒级延迟               |
| 锁竞争         | 没有共享可变状态，无需锁     | 省掉 spinlock/mutex 开销 |
| 缓存行失效     | 同一线程顺序访问，天然局部性 | CPU Cache 命中率飙升     |
| 原子性         | 命令执行天然不可分割         | 逻辑简化，无并发 Bug     |

#### 单线程的局限

数据规模增长 ～ 请求量增多 ～ 执行瓶颈主要在网络IO

#### 引入多线程（6.0后）

> 数据读写命令的处理依然是单线程，I/O与清理被多线程并行化，既保住低延迟，又缓解阻塞。

业务复杂 ～ QPS更高 ～ 对Redis性能有更高要求 ～ 搭建Redis集群 ～ 资源消耗巨大
读写命令单线程 ～ 不存在线程安全问题
大部分公司并发量不足以开启多线程 ～Redis6.0 默认多线程禁用

### IO模型

I/O 多路复用（epoll/kqueue）把网络事件转成回调，单线程也能轻松支撑 10w+ QPS。
I/O 多路复用本质上还是同步I/O

## 高效数据结构 + 算法

每个类型都有 “小而专” 的内存布局，避免通用容器浪费。

| 需求        | 底层实现                 | 复杂度          |
| ----------- | ------------------------ | --------------- |
| String      | SDS（O(1) 追加、预分配） | O(1)            |
| Hash/ZSET   | 跳表 + 哈希表 双索引     | O(log N)        |
| Set         | intset / dict 自适应     | O(1)            |
| HyperLogLog | 位运算 + 概率算法        | 12 KB 存 1e9 UV |

## 零拷贝网络 & 协议解析

- 内核提供的 writev/sendfile 直接把内存 buffer 推网卡，无需用户态拷贝
- RESP 协议简单，解析只需指针移动

## C 语言 + 精细编码

- 内存池、对象共享、预分配、内存对齐随处可见
- 代码路径短：一次 GET 命令 ≈ 几微秒级

## 附加加速手段（可选）

| 机制               | 作用                                                  |
| ------------------ | ----------------------------------------------------- |
| Pipeline           | 一次 RTT 处理 N 条命令                                |
| 连接复用           | 避免 TCP 三次握手                                     |
| 压缩列表/紧凑哈希  | 小对象内存再省 30%+                                   |
| 多线程 I/O（≥6.0） | 把网络读写 offload 到后台线程，主线程仍单线程执行命令 |

# 锁

## 分布式锁

```
set ex nx 命令 + lua脚本
```

- 多个客户端不会获得同一个资源锁
- 安全解锁和意外情况下锁的自动释放

### 命令

#### 加锁

```bash
SET ${lock_key} ${unique_value} EX ${expire_time} NX
```

- EX 30：30秒过期; PX 30：30毫秒过期
- NX：仅当key不存在时才设置成功（原子）
- 返回值：OK（拿到锁）/ nil（没拿到）
- unique_value：为了防止被别的客户端给释放了

#### 解锁

```bash
# 先 GET 比对 value 再 DEL，两段操作非原子！
if redis.call("GET",KEYS[1]) == ARGV[1] then
    return redis.call("DEL",KEYS[1])
else
    return 0
end
```

### Lua脚本

#### 加锁（等同 SET EX NX，但可扩展）

```lua
-- lock.lua
if redis.call("EXISTS",KeyS[1]) == 0 then
    redis.call("SET",KeyS[1], ARGV[1], "PX", ARGV[2])
    return 1
else
    return 0
end
```

```bash
EVAL lock.lua 1 user:123:lock  requestId  30000
```

#### 解锁（真正原子）

```lua
-- unlock.lua
if redis.call("GET",KeyS[1]) == ARGV[1] then
    return redis.call("DEL",KeyS[1])
else
    return 0
end
```

### 对比速查表

| 维度                   | SET EX NX 命令 | Lua 脚本      |
| ---------------------- | -------------- | ------------- |
| 加锁原子性             | ✅ 原子        | ✅ 原子       |
| 解锁原子性             | ❌ 需额外脚本  | ✅ 同一脚本   |
| 扩展逻辑（重入、续期） | ❌ 命令固定    | ✅ 随意加判断 |
| 性能                   | 略高（单命令） | 极低差距      |
| 易读性                 | 一行搞定       | 稍长          |

### 常见问题

把 TTL 留足、唯一 value 校验、看门狗续期、Redlock 保高可用，再加业务幂等兜底，才能把Redis分布式锁真正用到生产

```
Redis 分布式锁常见坑
├─ 时间维度
│  ├─ TTL 太短 → 看门狗续期
│  └─ 时钟漂移 → TTL 冗余
├─ 并发维度
│  ├─ 误删锁   → 唯一 value + Lua
│  ├─ 重入需求 → 计数器（Redisson）
│  └─ 公平需求 → ZooKeeper
├─ 高可用维度
│  ├─ 单点故障 → Redlock / 幂等兜底
│  └─ 网络分区 → 多数派 Redlock
└─ 性能维度
   ├─ 锁粒度   → 分段 / 分片
   └─ 重试风暴 → 指数回退
```

#### 锁提前过期（业务没跑完，锁没了）

现象：任务执行时间 > TTL，锁被Redis自动释放，导致并发冲突
根因：TTL 固定，无法预测 GC、网络抖动、慢 SQL 带来的耗时
解决：看门狗续期

```lua
-- renew.lua：只有 value 匹配才续期
if redis.call("GET",KeyS[1]) == ARGV[1] then
    return redis.call("PEXPIRE",KeyS[1], ARGV[2])
end
```

Java 侧可用 Redisson，内置 TimerTask 每 TTL/3 续期一次

#### 主从问题（主节点 crash，锁丢失）/ 单点故障

现象：主节点刚加锁还没同步到从节点就宕机，新主无锁，多客户端同时获得锁
根因：Redis 异步复制
解决：

- 高并发场景：使用 Redlock（≥3 独立 master）
- 轻量级场景：主从 + 哨兵 接受极小概率重复，业务幂等兜底

#### 网络分区

现象：客户的与redis之间的连接可能中断
根因：网络不稳定

#### 公平性问题

现象：网络延迟导致后到先得
根因：Redis 单线程按到达顺序处理请求
解决：

- 业务可接受：无需处理。
- 必须公平：改用 ZooKeeper 顺序临时节点

#### 时钟漂移

现象：各节点系统时间不一致，TTL 计算失真
根因：NTP 抖动或人为调时
解决：

- 通过NTP服务同步所有节点的系统时钟
- 把所有 TTL 都设得远大于最大漂移（经验 5×）
- Redlock 要求 TTL > 网络往返 + 漂移

#### 不可重入

现象：同一线程递归或嵌套调用时第二次加锁失败
根因：默认实现无计数器
解决：Redisson 提供可重入锁（内部用 hash 存线程计数器）

```java
RLock lock = redisson.getLock("order:123");
lock.lock();   // 计数器=1
lock.lock();   // 计数器=2
lock.unlock(); // 计数器=1
lock.unlock(); // 计数器=0，真正释放
```

#### 锁被“别人”误删

现象：A超时解锁，把B的锁删掉
根因：DEL前未校验锁归属
解决：唯一value（UUID+线程 ID）+ Lua原子删除

```lua
-- unlock.lua
if redis.call("GET",KeyS[1]) == ARGV[1] then
    return redis.call("DEL",KeyS[1])
else
    return 0
end
```

#### 高并发性能瓶颈

现象：多实例抢同一把锁，CPU/网络飙升
根因：锁粒度太粗或重试过于频繁
解决：

- 锁分段：按用户/订单分片，减少热点
- 指数回退重试：失败线程随机休眠再试

#### 锁粒度与死锁

现象：一把大锁锁住整个表，导致其他无关操作也排队
根因：设计阶段未拆分临界区
解决：

- 按业务键分片：lock:order:{orderId} 而非 lock:order:\*
- 加锁顺序：所有线程按全局顺序获取多把锁，避免循环等待

### RedLock

redlock是一种分布式锁的实现方案 ～ 解决分布式环境中redis实现分布式锁的安全性问题（当部分节点发生故障也不会影响锁的使用和数据问题的产生）

红锁实现成本较高（至少5个实例，依次加锁，性能比不上单例redis加锁，极端环境下会有问题） ～ 生产环境中用 主从+哨兵 方式部署

#### 基本思想

在 N 个独立的Redismaster 上同时抢锁，只要过半节点抢成功且总耗时小于锁 TTL 的一半，就认为自己拿到了‘全局’互斥锁

1. 多节点：部署 ≥5 个奇数、相互隔离的Redismaster（无复制）
1. 多数派：成功写入 ≥ N/2+1 个节点才算锁有效，否则释放所有已加锁的实例仔重试
1. 时间盒：整个抢锁过程耗时 < TTL/2，防止 时钟漂移/网络延迟 导致锁过期
1. 原子释放：解锁时向所有节点发送DEL，无论成败，保证最终一致性

#### 实现原理

N ≥ 5 个独立 Redis实例（彼此之间没有任何关系，不同于redis cluster，无复制、无哨兵、完全隔离、无需任何信息交互
多数派阈值 = ⌊N/2⌋ + 1（例如 5 台需 3 台成功）
时钟假设：节点间时钟漂移远小于TTL，可近似同步

#### 加锁流程

先计时，再逐点抢锁，过半且耗时 < TTL/2 才算成功，失败即全网释放

| 步骤          | 动作                    | 伪代码 / CLI                         | 备注                 |
| ------------- | ----------------------- | ------------------------------------ | -------------------- |
| 1️⃣ 取时间戳   | 记录当前毫秒 startMs    | startMs = System.currentTimeMillis() | 用于计算加锁耗时     |
| 2️⃣ 依次抢锁   | 向 N 个独立Redis发命令  | SET resource$uuid NX PX $ttl         | 每个实例互不影响     |
| 3️⃣ 统计结果   | 成功数 ≥ N/2+1          | successCnt++                         | 多数派原则           |
| 4️⃣ 计算耗时   | 总耗时 < ttl/2          | cost = now - startMs                 | 防网络延迟导致锁失效 |
| 5️⃣ 设置有效期 | 真实有效期 = ttl - cost | validity = ttl - cost                | 剩余时间才是安全窗口 |

#### 缺陷

复杂性：需要多个多个redis实例，增加系统的复杂性和维护成本
时间同步依赖：redlock依赖多阶段系统的时间一致性，如果不同步则可能影响锁的有效性
不适用高并发：需要访问多个实例导致同时尝试获取锁，可能会导致锁获取的性能下降
锁的存续问题：超时间的操作中可能需要手动续锁期，多个实例会增加实现的复杂性

#### 安全性

##### Martin Kleppmann 系统性总结

Redlock 的安全性建立在「网络延迟、进程暂停、时钟漂移」三个时间假设上；只要其中任何一个假设被现实打破，就可能出现「两个客户端同时认为自己持有锁」的概率性故障

##### 核心缺陷：依赖危险的时间假设

| 假设              | 现实反例                                | 触发概率 | 现场现象                                                       |
| ----------------- | --------------------------------------- | -------- | -------------------------------------------------------------- |
| 网络延迟 < TTL/2  | 跨机房丢包、交换机拥塞                  | 中       | Client 1 的请求在 TTL 内未到达半数节点 → Client 2 同时加锁成功 |
| 进程暂停 < TTL/2  | JVM Stop-The-World、Page Fault、SIGSTOP | 高       | Client 1 加锁后 GC 30s → 所有节点锁过期 → Client 2 也加锁成功  |
| 时钟漂移 < TTL/10 | NTP 跳变、闰秒、管理员手动改时          | 低~中    | Node C 时钟快进 5s → 锁提前过期 → Client 2 立即加锁成功        |

一旦上述任一假设失效，就会出现 “双客户端持锁” 的概率性冲突

##### 量化场景举例

GC 停顿场景

- Client 1 成功锁住 A/B/C/D/E
- GC 暂停 30 s，锁全部过期
- Client 2 重新加锁成功
- 结果：两客户端并发操作共享资源

时钟跳变场景

- Client 1 锁 A/B/C；C 节点时钟向前跳 5 s → 锁失效
- Client 2 锁 C/D/E；双方同时认为自己持有锁

节点重启无持久化

- C 崩溃重启且未持久化 → 锁记录丢失
- Client 2 立即重锁成功；Client 1 仍持有旧锁

##### Kleppmann 提出的根本矛盾

Redlock 处于同步模型假设下，而真实分布式系统是异步模型

- 同步模型（Redlock）：假设延迟、暂停、漂移都有上界
- 异步模型（Paxos/Raft/ZooKeeper）：不依赖时间，只依赖消息顺序；即使延迟无限大，也不会给出错误答案

##### 官方/社区回应

| 阵营                  | 观点                                                            | 结论                       |
| --------------------- | --------------------------------------------------------------- | -------------------------- |
| Martin Kleppmann      | Redlock 无法保证 safety，建议用 ZooKeeper/Etcd 或 Fencing Token | 弃用 Redlock 于强一致场景  |
| Antirez（Redis 作者） | 通过 延迟重启、TTL 冗余、运维约束可把概率降到极低，工程可接受   | 继续使用，但需业务幂等兜底 |

Redlock 不是绝对安全，而是“在可控的时钟、网络范围内，概率极低地失效”
若业务对 正确性零容忍 → 选择 ZooKeeper/Etcd
若可接受小于 0.1% 概率冲突 → Redlock + 幂等 + TTL冗余 仍可落地

# 主从复制

一主多从，异步同步 ～ 把主节点的数据变更近乎实时地同步到从节点，用于 读写分离、故障转移、数据备份
fork 快照 + backlog 差异 + 持续异步写流；配合哨兵即可在秒级完成主从切换，实现高可用读写分离

## 复制模型

| 角色            | 作用 | 备注                           |
| --------------- | ---- | ------------------------------ |
| Master          | 读写 | 唯一可写节点                   |
| Replica / Slave | 只读 | 可横向扩容读能力，支持级联复制 |

默认异步；2.8 起支持部分重同步（PSYNC），断线后只传差异

## 复制流程（一次握手 + 持续同步）

| 阶段          | 命令/动作                                      | 说明               |
| ------------- | ---------------------------------------------- | ------------------ |
| 1. 建立连接   | REPLICAOF 127.0.0.1 6379                       | 从节点发起         |
| 2. 同步 RDB   | 主节点 fork → dump RDB → 发文件                | 全量快照           |
| 3. 缓冲写命令 | 主节点把期间的写命令缓存在 replication backlog | 防止丢失           |
| 4. 发送差异   | PSYNC<replid> <offset>                         | 断线后只传缺失部分 |
| 5. 持续复制   | 主节点每执行一条写命令 → 发送给所有从节点      | 异步，无 ACK       |

## 复制原理

### 全量同步

从节点第一次上线 → 主节点 `fork` → 生成 RDB → 网络传输 → 从节点清空旧数据 → 载入 RDB → 追平增量 → 持续复制

#### 触发条件

- 首次建立复制（`REPLICAOF` 或 `SLAVEOF`）
- 主节点 `runID` 变化（主重启）
- 从节点 `PSYNC` 失败（backlog 不足）

#### 流程

| 步骤       | 主节点                         | 从节点                  | 说明                                                                      |
| ---------- | ------------------------------ | ----------------------- | ------------------------------------------------------------------------- |
| 连接握手   | 等待 `REPLICAOF`               | 发送 `PSYNC ? -1`       | 全量同步标识                                                              |
| 判断方式   | runid没值 -> 全量同步          | 存储runid和当前复制进度 | 主节点返回fullresync并带上主服务器runid和当前进度                         |
| 生成快照   | `fork` → RDB                   | —                       | 子进程写磁盘，主线程继续服务，RDB生成过程中的新写命令存replication buffer |
| 传输文件   | `send RDB`                     | 接收 RDB                | 通过 socket 直接流式传输                                                  |
| 清空旧数据 | —                              | `FLUSHDB`               | 从节点先清库再加载                                                        |
| 载入快照   | —                              | 阻塞式 `rdbLoad`        | 期间从节点不可读                                                          |
| 增量追平   | 把replication buffer发给从节点 | 执行缓冲命令            | 断线后只传差异                                                            |

### 增量同步

断线重连后只传“差异”而不是整个RDB，靠backlog环形缓冲区实现，核心依赖 复制偏移量（offset）+ 复制积压缓冲区（backlog）

#### 触发场景

- 网络闪断后 自动重连
- master_link_down_since_seconds 短暂 > 0 再恢复
- 哨兵/Cluster 故障转移后旧主变从重新连

#### 流程

| 步骤   | 主节点                                            | 从节点                         | 说明                     |
| ------ | ------------------------------------------------- | ------------------------------ | ------------------------ |
| ① 握手 | —                                                 | 发送 `PSYNC <replid> <offset>` | 请求从 `offset` 开始续传 |
| ② 判断 | 检查 `offset` 是否在 repl_backlog_butter 内       | —                              | 若不在 → 回退 全量同步   |
| ③ 续传 | 把 repl_backlog_buffer 中缺失字节流实时发给从节点 | 执行命令，追赶 offset          | 无阻塞、无磁盘 IO        |

### replication buffer V.S. repl_backlog_buffer

| 维度     | replication buffer                   | repl_backlog_buffer           |
| -------- | ------------------------------------ | ----------------------------- |
| 作用阶段 | 全量复制期间 + 增量复制期间          | 仅用于增量复制                |
| 数量     | 每个从节点各 1 个                    | 主节点全局只有 1 个           |
| 数据结构 | 线性缓冲区（动态增长）               | 固定大小环形缓冲区            |
| 生命周期 | 从节点连接存在期间                   | 主节点启动即创建，断线后保留  |
| 满了以后 | 断开连接 → 触发重连/全量同步         | 覆盖旧数据 → 回退全量同步     |
| 典型大小 | 受 `client-output-buffer-limit` 限制 | `repl-backlog-size` 默认 1 MB |

# 持久化

把内存数据写进磁盘，重启可恢复
官方提供 RDB（快照）与 AOF（追加日志）两种，可单独用，也可混合持久化

## RDB（Redis DataBase）

把某一时刻的内存数据拍一张快照，存成紧凑的二进制文件dump.rdb，重启时秒级恢复
redis.conf 可以配置x秒内有y个key发生变化就触发持久化操作

### 生成方式

| 触发 | 命令/配置                           | 备注                            |
| ---- | ----------------------------------- | ------------------------------- |
| 手动 | `SAVE`（阻塞）`BGSAVE`（后台 fork） | 线上只用 `BGSAVE`               |
| 自动 | `save 900 1`                        | 15 分钟 1 次写或 60 秒 1 万次写 |
| 关闭 | 注释所有 `save` 行                  | 纯缓存场景常用                  |

### 底层流程

检查子进程 → 触发持久化 rdbSaveBackground → 父进程 fork()，Copy-On-Write → 子进程写 RDB → 父进程继续服务 → RDB完成后替换旧RDB，子进程退出

- Copy-On-Write：fork时共享内存页，修改才复制（任一进程要修改内存，内存会重新复制一份给修改进程单独使用）→ 几乎零阻塞
- 耗时：1 GB 内存 ≈ 100 ms（SSD）

### 特点

| 优点                   | 缺点                                |
| ---------------------- | ----------------------------------- |
| 文件小、恢复快（秒级） | 快照粒度粗，可能丢 1 次快照周期数据 |
| 对性能影响极低         | 不支持实时持久化                    |

| 维度     | 内容                                        |
| -------- | ------------------------------------------- |
| 文件     | `dump.rdb` 二进制快照                       |
| 触发方式 | 手动 `SAVE`/`BGSAVE`；配置 `save m n`       |
| 原理     | `fork()` 子进程 → 生成 RDB → 父进程继续服务 |
| 优缺点   | 小文件、恢复快；秒级粒度、丢数据            |
| 配置     | `save 900 1`（15 min 内 1 次写即快照）      |

## AOF（Append Only File）

把所有写命令实时追加到磁盘，重启时逐条重放，实现秒级/毫秒级的数据持久化

### 工作方式

| 维度     | 描述                                        |
| -------- | ------------------------------------------- |
| 文件     | `appendonly.aof` 纯文本命令序列             |
| 写入策略 | 每条写命令 → 追加到 AOF 缓冲区 → 按策略刷盘 |
| 恢复     | 重启后逐条重放命令                          |

### 三种刷盘策略

| 策略             | 配置                   | 数据安全     | 性能 |
| ---------------- | ---------------------- | ------------ | ---- |
| always           | `appendfsync always`   | 最高（不丢） | 最慢 |
| everysec（默认） | `appendfsync everysec` | 最多丢 1 秒  | 折中 |
| no               | `appendfsync no`       | 可能丢很多   | 最快 |

### AOF 重写（压缩瘦身）

写操作增加 ～ AOF文件变大 ～ 恢复速度变慢+消耗大量磁盘 -> 重写机制压缩文件（通过最少的命令重新生成一个等效AOF文件）
重写后文件体积可缩小 10~100 倍

```
// AOF重写不是对现有的AOF文件进行修改
// 而是根据当前每个键的最新值转换为对应的写命令，写入新的AOF文件
(set A 1) + (set A 2) + (set A 3) -> (set A 3) 前两个是历史值没用了
```

| 触发 | 命令/配置                                                            | 作用                       |
| ---- | -------------------------------------------------------------------- | -------------------------- |
| 手动 | `BGREWRITEAOF`                                                       | 后台 fork → 生成最小命令集 |
| 自动 | `auto-aof-rewrite-percentage 100` & `auto-aof-rewrite-min-size 64mb` | 文件翻倍且 ≥64 MB 自动重写 |

### 底层流程

父进程 fork() → 子进程写新的AOF文件 → 主进程处理新写入的命令（追加到现有AOF和aof_rewrite_buffer） → 合并新命令（缓冲区新命令追加到新AOF文件） → 替换旧的AOF文件

### 特点

| 优点                | 缺点                          |
| ------------------- | ----------------------------- |
| 秒级/毫秒级数据安全 | 文件大，恢复慢（GB 级需分钟） |
| 可读、可审计        | 重写时 fork 消耗内存/CPU      |

| 维度     | 内容                                        |
| -------- | ------------------------------------------- |
| 文件     | `appendonly.aof` 纯文本命令流               |
| 同步策略 | `always`/`everysec`/`no`（默认 `everysec`） |
| 重写机制 | `BGREWRITEAOF` 压缩历史命令                 |
| 优缺点   | 秒级/毫秒级数据安全；文件大，恢复慢         |
| 配置     | `appendonly yes` `appendfsync everysec`     |

### 文件修复

redis-check-aof 工具

### MP-AOF

Redis 在 v7.0 引入了 MP-AOF（Multi-Part AOF），通过清单文件（Manifest）管理多个 AOF 片段，避免因重写失败或崩溃导致数据丢失，同时支持增量式数据管理，便于备份、迁移和恢复
多片段AOF + Manifest索引，通过增量重写和分段管理，减少磁盘 I/O 压力，提升重写效率，同时提供更安全的数据恢复机制

| 文件类型        | 作用                                                                                                                                        |
| --------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| Base AOF        | 初始全量数据的快照，类似 RDB，格式为 RDB-AOF 混合文件，文件后缀为 `.base.rdb`                                                               |
| Incremental AOF | 记录基础文件生成后的增量写操作，后缀为 `.incr.aof`                                                                                          |
| Manifest 文件   | 记录所有 AOF 文件的元数据（如文件名、序列号、类型），用于指导Redis启动时的数据恢复顺序，文件格式为 JSON，文件名为 `appendonly.aof.manifest` |

#### 文件结构

```
appendonlydir/
├── base.aof         # 主数据快照，来自一次 AOF 重写（或 RDB）
├── incr-0001.aof    # 第1段增量命令
├── incr-0002.aof    # 第2段增量命令
├── manifest.aof     # 管理上述文件段的“索引”，告诉Redis读取顺序
```

#### 重写流程优化

MP-AOF 的重写流程与传统 AOF 重写有所不同：

1. 增量重写：不再生成完整新文件，而是将增量数据追加到现有文件，减少磁盘 I/O 压力
1. 原子性切换：重写完成后，通过更新清单文件实现新旧文件原子替换，避免数据不一致

#### 优缺点

| 特性       | 传统 AOF           | MP-AOF                     |
| ---------- | ------------------ | -------------------------- |
| 文件结构   | 一个巨大文件       | 多段（base + incr）        |
| 重写负担   | 高 IO 峰值         | 平滑处理，主线程几乎无感知 |
| 新命令写入 | 重写期间缓存在内存 | 分段写入增量 AOF           |
| 安全性     | 替换失败风险       | 有 manifest，增量保留      |
| 可恢复性   | 单文件损坏 = 崩溃  | 分段结构更可控             |

## 混合持久化（≥4.0）

RDB 头 + AOF 尾，重启先载入 RDB，再重放 AOF 增量

- 配置：`aof-use-rdb-preamble yes`
- 优点：快速加载 + 低数据丢失。
- 触发：AOF重写的时候

## 恢复流程

1. AOF 开启 → 优先 AOF；
1. AOF 关闭 → 用 RDB；
1. 混合开启 → 先 RDB 再 AOF。

## 选型速查表

| 场景        | 推荐方案                   |
| ----------- | -------------------------- |
| 纯缓存      | RDB 关闭，重启重新加载即可 |
| 可丢 1 秒   | RDB + AOF everysec         |
| 金融级 0 丢 | AOF always（性能最低）     |

# 数据过期后的删除策略

## 定期删除（Scheduled/Periodic Expiry）

原理：Redis 默认每隔一阵（默认100毫秒）随机抽取部分设置了过期时间的键，检查这些键是否过期。如果过期，则将其删除。如果在一批键中发现过期键的比例较高，Redis 会重复进行清理
优点：较为平衡 CPU 和内存的使用，避免了定时删除的高 CPU 负担和惰性删除的内存浪费
缺点：不能保证所有过期键都能被及时清理

## 定时删除（Passive Expiry）

原理：Redis 在设置键的过期时间时，会在过期字典中记录该键的过期时间。当到达过期时间点时，Redis 会自动安排删除任务
优点：能够及时释放内存，避免过期数据占用空间
缺点：如果大量键同时过期，会增加 CPU 负担

## 惰性删除（Lazy Expiry）

原理：当客户端尝试访问某个键时，Redis 会检查该键是否已经过期。如果键已过期，Redis 会在访问时立即删除该键
优点：节省 CPU 资源，因为Redis只在访问键时才进行检查
缺点：可能导致内存浪费，因为未被访问的过期键不会被删除

## 内存回收 / 主动删除

Redis内存回收是通过逐出（eviction）策略来实现的，当内存达到配置的限制时，根据设定的规则自动删除一些键，以释放内存空间
Redis 内存回收通过maxmemory和 maxmemory-policy配置实现，多种逐出策略适用于不同场景，确保内存使用在限制范围内

### 逐出策略

| 策略            | 说明                                  |
| --------------- | ------------------------------------- |
| noeviction      | 不逐出任何数据，当内存不足时返回错误  |
| allkeys-lfu     | 逐出最近最少使用的键，按LFU顺序       |
| allkeys-lru     | 逐出最近最少使用的键，按LRU顺序       |
| allkeys-random  | 随机逐出键                            |
| volatile-lfu    | 仅逐出设置了过期时间的键，按 LFU 顺序 |
| volatile-lru    | 仅逐出设置了过期时间的键，按 LRU 顺序 |
| volatile-random | 随机逐出设置了过期时间的键            |
| volatile-ttl    | 优先逐出快到期的键                    |

### 配置内存限制

在Redis配置文件中，可以通过以下参数设置内存限制和逐出策略：

```conf
maxmemory <bytes>          # 设置最大内存限制
maxmemory-policy <policy>  # 设置逐出策略
```

### 流程

1. 内存达到限制：当Redis使用的内存量达到 `maxmemory` 设置的值时，触发内存回收。
1. 选择策略：根据 `maxmemory-policy` 配置的策略选择要删除的键。
1. 逐出键：根据策略逐出键，释放内存空间。
1. 持续监控：Redis 会持续监控内存使用情况，当内存使用量低于限制时停止逐出。

### 注意事项

- 内存碎片：大量小对象可能导致内存碎片，影响内存回收效率
- 逐出策略调整：根据实际业务需求选择合适的逐出策略，避免不必要的数据丢失
- 监控内存使用：使用 `INFO MEMORY` 命令监控内存使用情况，及时调整配置

# 集群

Redis集群（Redis Cluster）是官方提供的分布式解决方案，用于解决单机Redis的容量和性能瓶颈，同时提供高可用性，通过哈希槽分片和主从复制实现分布式存储，支持水平扩展和高可用性，适合大规模数据存储和高并发访问场景

## 架构设计

多个实例组成，每个实例存储部分数据（实例之间的数据不重复）
主从复制 + 分片，通过16384个槽位（slots）分布数据。每个槽位可以分配到不同的节点，支持水平扩展和高可用性

- 主节点（master）：负责存储数据和处理读写请求
- 从节点（replica）：主节点的副本，用于数据备份和故障转移
- 每个节点都保存集群的完整拓扑信息（每个节点的ID、IP地址、端口、哈希槽范围 等）

集群通过哈希槽（hash slots）来实现数据分片，每个键通过哈希算法映射到一个槽位，槽位再分配到不同的节点

```bash
slot = CRC16(key) % 16384
```

- 槽位分配：集群启动时，槽位会均匀分配到各个节点
- 数据迁移：当节点数量变化时，槽位会重新分配，数据也会自动迁移

客户端发送请求，通过集群的任意节点连接，该节点：

- 存了目标数据，直接返回
- 没存目标数据，根据请求键值计算哈希槽并录到到正确节点

## 信息同步

### Gossip协议

#### 过程

定期发送 PING 消息：每个节点每隔一段时间（默认每秒）会随机选择几个其他节点，向它们发送 PING 消息。PING 消息中包含了发送节点自身的状态信息，如节点的 ID、IP、端口、槽位分配情况等

接收并回复 PONG 消息：接收到 PING 消息的节点会回复一个 PONG 消息。PONG 消息中也包含了该节点自身的状态信息。发送 PING 消息的节点收到 PONG 消息后，会根据消息内容更新自己对其他节点状态的记录

传播状态信息：节点在收到其他节点的状态信息后，会将这些信息传播给其他节点。这样，每个节点都能逐渐了解到整个集群中所有节点的状态

处理 FAIL 消息：当某个节点认为另一个节点已经失效时，会发送 FAIL 消息给其他节点。其他节点收到 FAIL 消息后，会将该节点标记为失效。

新节点加入：新节点加入集群时，会发送 MEET 消息给集群中的某个节点。该节点收到 MEET 消息后，会将新节点加入集群，并开始与其他节点进行通信

#### 消息类型及作用

| 消息类型 | 作用                                                                                   |
| -------- | -------------------------------------------------------------------------------------- |
| PING     | 节点定期发送给其他节点，用于检测节点是否存活，并携带自身的状态信息                     |
| PONG     | 收到 PING 消息的节点回复给发送者，表示自己还活着，并携带自身的状态信息                 |
| MEET     | 当一个新节点加入集群时，发送给集群中的某个节点，告知自己的存在，让该节点将自己加入集群 |
| FAIL     | 当一个节点认为另一个节点已经失效时，发送给其他节点，告知该节点已失效                   |
| PUBLISH  | 节点接收到客户端的 PUBLISH 命令后，会将消息广播给集群中的所有节点                      |
| UPDATE   | 用于更新节点的状态信息，例如节点的角色、槽位分配等                                     |

#### 特点

快速收敛：快速传播信息，确保集群状态迅速更新
分布式信息传播：每个节点定期向其他节点发送状态信息，确保所有节点对集群状态有一致视图
降低网络负担：信息是随机节点间对话传播，避免了集中式状态查询
低延迟和高效率：轻量级通信方式，能够快速传播信息
去中心化：没有中心节点，所有节点平等地参与信息传播，避免了单点故障
可扩展性强：可以方便地添加或删除节点
容错性好：即使部分节点故障，也不会影响整个集群的运行

### 集群分片

#### 原理图示

Redis 集群由多个节点组成，每个节点负责存储一部分数据。集群总共有 16384 个槽位，每个槽位对应一个唯一的哈希值范围。

```
Master-1 (0-5460)     Master-2 (5461-10922)    Master-3 (10923-16383)
    |                       |                        |
Slave-1              Slave-2                  Slave-3
```

#### 槽位分配

使用 MurmurHash算法 来计算键的哈希值，该算法具有良好的分布性，能够确保键均匀分布到各个槽位
槽位会均匀分配到各个节点。例如，一个 3 主 3 从的集群，槽位分配可能如下：

| 节点     | 槽位范围    |
| -------- | ----------- |
| Master-1 | 0-5460      |
| Master-2 | 5461-10922  |
| Master-3 | 10923-16383 |

如果槽被转移而客户端还未来得及更新槽信息，当前实例没有目标数据，则返回MOVED响应给客户端，将其重定向到对应实例

## 故障转移

Redis 集群支持 自动故障转移，当主节点故障时，从节点会自动晋升为主节点，客户端会自动重连到新的主节点

- 哨兵机制：每个从节点都会监控主节点的状态，当主节点故障时，从节点会投票选举新的主节点
- 客户端重连：客户端通过重定向机制自动连接到新的主节点

#Key

## 存储

假设我们有一个 3 主 3 从的Redis集群，槽位分配如下：

| 节点     | 槽位范围    |
| -------- | ----------- |
| Master-1 | 0-5460      |
| Master-2 | 5461-10922  |
| Master-3 | 10923-16383 |

### 哈希槽计算

```bash
key1 -> value1
key2 -> value2
key3 -> value3
```

```python
import crc16
def calculate_slot(key):
    return crc16.crc16xmodem(key.encode()) % 16384
slot1 = calculate_slot("key1")  # 假设结果为 1234
slot2 = calculate_slot("key2")  # 假设结果为 7890
slot3 = calculate_slot("key3")  # 假设结果为 13578
```

### 确定节点

根据槽位范围，确定每个键应该存储在哪个节点：

-Key1（槽位 1234）→ Master-1
-Key2（槽位 7890）→ Master-2
-Key3（槽位 13578）→ Master-3

### 写入数据

```python
import redis
# 连接到 Master-1
client1 = redis.Redis(host='127.0.0.1', port=7000)
client1.set("key1", "value1")
# 连接到 Master-2
client2 = redis.Redis(host='127.0.0.1', port=7001)
client2.set("key2", "value2")
# 连接到 Master-3
client3 = redis.Redis(host='127.0.0.1', port=7002)
client3.set("key3", "value3")
```

## 请求

### 哈希槽计算

```bash
key1 -> value1
key2 -> value2
key3 -> value3
```

```python
import crc16
def calculate_slot(key):
    return crc16.crc16xmodem(key.encode()) % 16384
slot1 = calculate_slot("key1")  # 假设结果为 1234
slot2 = calculate_slot("key2")  # 假设结果为 7890
slot3 = calculate_slot("key3")  # 假设结果为 13578
```

### 查询请求

客户端连的是node1，数据存储在node3
如果客户端请求的键不在当前节点，node1会返回一个重定向响应，客户端会自动连接到正确的节点

- MOVED：键已迁移，客户端需要连接到指定的节点（返回目标节点信息，如node3的IP和端口）
- ASK：键需要在另一个节点上进行操作，客户端需要先发送一个 `ASKING` 命令。

```Shell
# 客户端收到 MOVED 响应
MOVED <slot> <ip>:<port>
# 客户端连接到指定的节点并重试请求

# 客户端收到 ASK 响应
ASK <slot> <ip>:<port>
# 客户端发送 ASKING 命令到指定的节点，然后重试请求
```

### 重新连接

客户端根据返回信息与node3建立连接，并再次发送查询请求，得到数据

## 16384 (2^14)

Redis 集群选择 16384 个槽位是基于性能、可扩展性和灵活性的综合考虑

### 为什么是 16384？

- 2的幂：16384是2的14次方，这种设计使得哈希计算和槽位分配更加高效。使用2的幂可以利用位运算，减少计算复杂度
- 足够细粒度：16384个槽位提供了足够的细粒度，使得数据可以在集群中均匀分布。即使在大规模集群中，每个槽位的数据量也不会过大，从而保证了集群的负载均衡
- 性能与内存平衡：16384个槽位在性能和内存使用之间取得了平衡。如果槽位数量过多，会增加内存开销和管理复杂度；如果槽位数量过少，会导致数据分布不均匀，影响负载均衡

### 为什么不是 65536

理论上 crc16() 可以支持2^16个

- 消息大小：正常心跳包要带上节点的完整配置数据且心跳频繁，如果用2^14需要2k数据，但用2^16要8k数据（槽位信息用长度16384位的数组表示，1/0区分）
- 集群规模：集群不太可能超过1000个节点，16384足够用且每个分片下的槽又不会太少

## HotKey

指在Redis中被频繁访问的Key或者带宽占比过大的Key
由于Redis是单线程的，大量请求集中在少数几个Key上会导致性能瓶颈（消耗大量CPU、降低吞吐量、流量不均衡、服务不可用）

### 影响

性能瓶颈：大量请求集中在少数key上，导致单线程处理不过来，响应时间变长
内存压力：热点key可能占用大量内存，影响其他数据的存储
网络带宽：频繁访问热点key会占用大量网络带宽，影响其他请求的处理

### 检测

#### 根据业务情况提前预估

根据业务经验预估可能的热点Key，例如参与秒杀活动的商品数据

- 优点：简单易行，无需额外工具
- 缺点：无法预估所有热点key的出现，如突发热点事件；且对业务能力有一定要求

#### 业务代码中记录分析

在业务代码中添加逻辑，对每个key的访问情况进行记录和分析

- 优点：可以精确统计每个key的访问频率，适合业务定制化需求
- 缺点：增加了业务代码的复杂性，可能影响性能

#### redis集群监控

查看集群中哪个Redis出现QPS倾斜，该实例有极大概率存在热点Key

- 优点：使用简单（企业的Redis大多数是集群）
- 缺点：每次发生情况都要排查，而且QPS倾斜也不都是热点Key导致的

#### 使用 hotkeys

Redis 4.0 及以上，redis-cli 提供了 --hotkeys 选项，帮助分析哪些Key是热点Key（scan + object freq 实现）

- 优点：使用方便，可以直接输出热点Key及其访问次数
- 缺点：需要Redis服务器的 maxmemory-policy 参数设置为LFU算法，否则无法使用；需要扫描keyspace，若key数量多则导致执行时间长且实时性不好

```bash
redis-cli --hotkeys
```

#### 使用 MONITOR 命令

MONITOR命令可以实时监控Redis服务器执行的所有命令。通过分析这些命令（结合日志和相关分析工具），可以观察到哪些Key被频繁访问

- 优点：实时性强，可以即时发现热点Key
- 缺点：对性能影响较大，不建议在生产环境中长时间使用

```bash
redis-cli monitor
```

#### 客户端收集

操作Redis之前加上统计键查询频次的逻辑，将统计数据发给一个聚合计算平台

- 优点：性能损耗低
- 缺点：成本较大

#### 代理层收集

有些服务在请求Redis之前会请求一个代理服务，在代理层收集数据逻辑类似于客户端收集

- 优点：使用方便，无需考虑SDK多语言异构差异和升级成本
- 缺点：要增加代理层，进行转发等操作，代理有成本，转发有性能损耗

#### 使用 LFU 算法

Redis 4.0 引入了基于 LFU（Least Frequently Used）的热点Key发现机制。通过配置 maxmemory-policy 为 volatile-lfu 或 allkeys-lfu，可以记录每个Key的访问频率

- 优点：可以长期记录Key的访问频率，适合动态发现热点Key
- 缺点：需要合理配置LFU相关参数（如 lfu-log-factor 和 lfu-decay-time），否则可能影响准确性

```conf
maxmemory-policy volatile-lfu
```

### 解决方案

一般结合一级缓存（本地缓存）和二级缓存（Redis缓存）

#### 常用方法

本地缓存：在客户端缓存热点key的值（如JVM内存中的缓存），设置合理的缓存失效时间，避免缓存穿透

分布式缓存：将热点key分片存储在不同的Redis实例中，分散请求压力；使用一致性哈希算法，将热点key分布到多个节点上

数据预热：在应用启动时，预先加载热点key的值，减少首次访问的延迟，定时刷新热点key的值，确保数据的时效性

限流：令牌桶算法（限制对热点key的访问频率，避免过多请求集中访问），漏桶算法（平滑处理请求，避免突发流量对Redis的冲击）

数据结构优化：Bitmap（对于布尔类型的数据），HyperLogLog（对于去重计数的场景）

异步加载：在后台线程中异步加载热点key的值，避免阻塞主线程；使用队列管理预加载任务，确保任务的有序执行

读写分离：主从复制（使用主从复制，将读请求分发到多个从节点，减轻主节点的压力），哨兵机制（自动切换主从节点，保证高可用性）

数据分片：哈希分片（将热点key按照哈希值分片存储，分散请求压力），范围分片（将热点key按照范围分片存储，便于管理和维护）

数据降级：在高并发场景下，对热点key的数据进行降级处理，减少数据量；对热点key的数据进行压缩，减少内存占用

数据迁移：动态迁移（在运行时动态迁移热点Key，避免单个节点压力过大），定期迁移（定期检查热点key的分布情况，进行数据迁移）

#### 热点Key的拆分

将一个热点Key拆分成多个Key，分散到不同的Redis节点上，从而降低单个节点的负载

##### 哈希打散

将一个热点Key拆分成多个Key，每个Key后面加一个后缀名，然后把这些Key分散到多个Redis节点上。客户端请求时，根据一定的规则计算出一个固定的Key，这样多次请求就会被分散到不同的节点上

```bash
hotkey_1
hotkey_2
...
hotkey_n
```

##### 数据分片

将热点Key的数据分片存储在多个Redis实例中。例如，将一个包含大量元素的Hash类型Key拆分成多个小的HashKey，每个小的HashKey存储部分元素

```bash
hash_key_1: {field1, field2}
hash_key_2: {field3, field4}
...
hash_key_n: {fieldN-1, fieldN}
```

##### 随机前缀

为热点Key添加随机前缀，使这些Key分散到不同的Redis节点上。客户端在访问时，根据前缀计算出目标Key

```bash
random_prefix_1:hotkey
random_prefix_2:hotkey
...
random_prefix_n:hotkey
```

##### 复制热点Key

在Redis集群中，将热点Key复制到多个节点上，使请求可以分散到多个节点，减轻单个节点的压力。

```bash
hotkey_copy_1
hotkey_copy_2
...
hotkey_copy_n
```

## BigKey

指那些存储的数据量过大（大型字符串 或 大型哈希表），可能导致性能问题的Key
大Key可能包含大量的数据或元素，这会带来一系列问题，如内存倾斜、网络阻塞、Redis 阻塞等

### 检测

#### 使用 bigkeys

通过 redis-cli 的 --bigkeys 参数可以快速扫描并找出大Key。该命令会遍历实例中的Key，并返回每种数据类型中最大的Key

- 优点：方便、快速
- 缺点：可能影响实例性能，建议在低峰时段执行

```bash
redis-cli --bigkeys
```

#### 使用监控工具

如 RedisInsight、Prometheus + Grafana 等，可以实时监控Redis实例的内存使用情况，帮助识别大Key

#### 使用 RdbTools

RdbTools 是一个第三方工具，可以解析Redis的RDB文件，帮助找出大Key

### 解决方案

#### 开发方面

压缩数据：对大Key的数据进行压缩，减少内存占用和网络传输开销。可以使用 Gzip、Snappy 等压缩算法

```python
import gzip
compressed_data = gzip.compress(data)
```

分片数据：将一个大Key拆分成多个小Key，分散存储在不同的Redis实例或节点上。例如，将一个大Hash拆分成多个小Hash，每个小Hash 存储部分字段

```python
# 示例：将一个大 Hash 拆分成多个小 Hash
for shard in range(1, 10):
    shard_Key= f"big_hash_shard_{shard}"
    # 将部分字段存储到 shard_Key中
```

优化数据：使用合适的数据结构存储。例如，一些用String的场景可考虑使用Hash、Set等结构优化

#### 业务层面

调整存储策略：只存必要数据（仅存用户ID、姓名等，而不存地址等不常用信息），将不适用的数据存储到其他存储系统（如OSS）

优化业务逻辑：从根源上避免大Key产生（一些可以不展示的信息直接删除）

#### 部署方面

使用集群：用集群方式部署Redis，将大Key拆分散落到不同服务器上

限数据量：通过Redis的 maxmemory 参数限制单个Key的数据量，避免 BigKey的出现

二级缓存：在应用层使用本地缓存（如 Caffeine、Guava Cache）缓存大Key的数据，减少对Redis的直接访问

# 缓存

预防胜于治疗：

- 热点Key设计时就考虑缓存击穿问题
- 合理设置缓存过期时间，避免集中失效
- 使用多级缓存减轻底层压力

多层次防护：

- 应用层：参数校验、限流、降级
- 缓存层：多级缓存、随机过期
- 数据库层：只读、限流

完善的监控：

- 缓存命中率监控
- 数据库连接池监控
- 异常流量监控

应急处理机制：

- 熔断机制
- 降级策略
- 自动恢复

## 缓存击穿 (Cache Breakdown)

当一个热点Key在失效瞬间，大量并发请求同时访问这个Key，由于缓存未命中，所有请求直接打到数据库上，造成数据库压力剧增

- 针对特定的热点数据
- 发生在Key失效时间点
- 大量并发请求

解决方案

1. 互斥锁/分布式锁

```go
// 使用互斥锁处理缓存击穿
func GetWithMutex(key string) (interface{}, error) {
    // 1. 先尝试从缓存获取
    val, err := cache.Get(key)
    if err == nil {
        return val, nil
    }

    // 2. 缓存未命中，获取互斥锁
    lockKey := "lock:" + key
    lock, err := redis.Get(lockKey, 10*time.Second) // 锁10秒
    if err != nil {
        return nil, err
    }

    // 3. 再次检查缓存，防止其他线程已经重建
    val, err = cache.Get(key)
    if err == nil {
        redis.Del(lockKey) // 释放锁
        return val, nil
    }

    // 4. 从数据库加载数据
    val, err = db.Query(key)
    if err != nil {
        redis.Del(lockKey) // 释放锁
        return nil, err
    }

    // 5. 写入缓存
    cache.Set(key, val, 30*time.Minute)

    // 6. 释放锁
    redis.Del(lockKey)
    return val, nil
}
```

2. 永不过期/逻辑过期

```go
// 方法1: 永不过期，后台定时更新
func SetWithExpire(key string, value interface{}, expiration time.Duration) {
    // 设置永不过期
    cache.Set(key, value, 0) // 不过期

    // 启动后台协程定期更新
    go func() {
        time.Sleep(expiration)
        newVal, err := db.Query(key)
        if err == nil {
            cache.Set(key, newVal, expiration)
        }
    }()
}

// 方法2: 逻辑过期，数据带有过期时间但不设置缓存TTL
type CacheItem struct {
    Value      interface{}
    ExpireTime time.Time
}

func GetWithLogicalExpire(key string) (interface{}, error) {
    // 1. 从缓存获取
    item, err := cache.Get(key)
    if err == nil {
        // 2. 检查是否过期
        cacheItem := item.(CacheItem)
        if time.Now().Before(cacheItem.ExpireTime) {
            return cacheItem.Value, nil
        }

        // 已过期但未更新，返回旧值
        return cacheItem.Value, nil
    }

    // 缓存中没有数据，查询数据库
    value, err := db.Query(key)
    if err != nil {
        return nil, err
    }

    // 写入缓存，带上过期时间
    expireTime := time.Now().Add(30 * time.Minute)
    cache.Set(key, CacheItem{Value: value, ExpireTime: expireTime}, 0)

    return value, nil
}
```

## 缓存穿透 (Cache Penetration)

查询不存在的数据，由于缓存中没有，请求会直接打到数据库上。如果有大量这类请求，会导致数据库压力剧增

- 查询的数据不存在
- 大量并发请求

解决方案

1. 缓存空值

```go
// 缓存空值解决缓存穿透
func GetWithNullCache(key string) (interface{}, error) {
    // 1. 先尝试从缓存获取
    val, err := cache.Get(key)
    if err == nil {
        if val == nil {
            // 缓存中的空值
            return nil, nil
        }
        return val, nil
    }

    // 2. 从数据库查询
    val, err = db.Query(key)
    if err != nil {
        return nil, err
    }

    // 3. 写入缓存（包括空值）
    if val == nil {
        cache.Set(key, nil, 5*time.Minute) // 空值也缓存
    } else {
        cache.Set(key, val, 30*time.Minute)
    }

    return val, nil
}
```

2. 布隆过滤器 (Bloom Filter)

```go
// 初始化布隆过滤器
var bf *bloomfilter.BloomFilter
var bfMutex sync.RWMutex

func initBloomFilter() {
    // 初始化布隆过滤器，容量100万，误判率0.01%
    bf = bloomfilter.New(1000000, 0.01)

    // 从数据库加载已存在的key到布隆过滤器
    keys, _ := db.GetAllKeys()
    for _, key := range keys {
        bfMutex.Lock()
        bf.AddString(key)
        bfMutex.Unlock()
    }
}

// 使用布隆过滤器过滤
func GetWithBloomFilter(key string) (interface{}, error) {
    // 1. 布隆过滤器检查
    bfMutex.RLock()
    exists := bf.TestString(key)
    bfMutex.RUnlock()

    if !exists {
        // 数据一定不存在，直接返回
        return nil, nil
    }

    // 2. 尝试从缓存获取
    val, err := cache.Get(key)
    if err == nil {
        if val == nil {
            return nil, nil
        }
        return val, nil
    }

    // 3. 从数据库查询
    val, err = db.Query(key)
    if err != nil {
        return nil, err
    }

    // 4. 写入缓存（包括空值）
    if val == nil {
        cache.Set(key, nil, 5*time.Minute)
    } else {
        // 更新布隆过滤器
        bfMutex.Lock()
        bf.AddString(key)
        bfMutex.Unlock()

        cache.Set(key, val, 30*time.Minute)
    }

    return val, nil
}
```

3. 参数校验和异常限制

```go
// 前端参数校验
func isValidKey(key string) bool {
    // 检查key格式是否符合业务要求
    return len(key) > 0 && len(key) <= 100 &&
           regex.MatchString(key)
}

// 异常请求限制
var reqCounter = &counter.ConcurrentCounter{}

func GetWithLimit(key string) (interface{}, error) {
    // 1. 参数校验
    if !isValidKey(key) {
        return nil, errors.New("invalid key")
    }

    // 2. 检查异常请求频率
    current := reqCounter.Incr(key)
    defer reqCounter.Decr(key)

    if current > 100 { // 同一key每秒最多100次请求
        return nil, errors.New("too many requests")
    }

    // 3. 正常缓存逻辑
    return GetWithNullCache(key)
}
```

## 缓存雪崩 (Cache Avalanche)

缓存中大量Key在同一时间失效，导致大量请求直接打到数据库上；或者Redis服务宕机，导致所有请求直接访问数据库

- 大量Key同时失效
- 缓存服务不可用
- 数据库压力剧增

解决方案

1. 错开过期时间

```go
// 为Key添加随机过期时间
func SetWithRandomExpire(key string, value interface{}, baseExpiration time.Duration) {
    // 添加随机时间，避免大量Key同时过期
    randomExpiration := time.Duration(rand.Intn(300)) * time.Second // 0-5分钟随机
    expiration := baseExpiration + randomExpiration

    cache.Set(key, value, expiration)
}

// 批量设置Key时添加随机过期
func SetMultipleWithRandomExpire(items map[string]interface{}, baseExpiration time.Duration) {
    for key, value := range items {
        SetWithRandomExpire(key, value, baseExpiration)
    }
}
```

2. 多级缓存

```go
// 实现多级缓存
type MultiLevelCache struct {
    l1Cache *redis.Client    // 本地缓存，如Caffeine
    l2Cache *redis.Client    // Redis集群
    db      *sql.DB          // 数据库
}

func (m *MultiLevelCache) GetWithMultiLevel(key string) (interface{}, error) {
    // 1. 先查本地缓存
    val, err := m.l1Cache.Get(key)
    if err == nil {
        return val, nil
    }

    // 2. 再查Redis
    val, err = m.l2Cache.Get(key)
    if err == nil {
        // 回填到本地缓存
        m.l1Cache.Set(key, val, 5*time.Minute)
        return val, nil
    }

    // 3. 最后查数据库
    val, err = m.db.Query(key)
    if err != nil {
        return nil, err
    }

    // 4. 回填到各级缓存
    m.l2Cache.Set(key, val, 30*time.Minute)
    m.l1Cache.Set(key, val, 5*time.Minute)

    return val, nil
}
```

3. 缓存降级

```go
// 缓存降级策略
type CacheDegradation struct {
    localCache *LocalCache
    redisCache *RedisCache
    db         *Database
    circuit    *circuit.Breaker
}

func (c *CacheDegradation) Get(key string) (interface{}, error) {
    // 1. 检查熔断器
    if c.circuit.IsOpen() {
        // 熔断状态，直接访问数据库
        return c.db.Query(key)
    }

    // 2. 检查缓存状态
    if c.redisCache.IsDown() {
        // 缓存服务不可用，启用降级策略
        return c.handleDegradation(key)
    }

    // 3. 正常缓存逻辑
    return c.normalGet(key)
}

func (c *CacheDegradation) handleDegradation(key string) (interface{}, error) {
    // 1. 尝试从本地缓存获取
    val, err := c.localCache.Get(key)
    if err == nil {
        return val, nil
    }

    // 2. 限制数据库访问频率
    if !c.circuit.AllowRequest() {
        return nil, errors.New("degraded mode: too many requests")
    }

    // 3. 从数据库查询并更新本地缓存
    val, err = c.db.Query(key)
    if err == nil {
        c.localCache.Set(key, val, 10*time.Minute)
    }

    return val, err
}
```

# 与数据库的数据一致性

数据不一致的常见原因

- 更新顺序问题：先更新缓存后更新数据库，或反之
- 并发操作：多个线程同时读写
- 缓存雪崩/击穿：大量缓存失效导致数据库压力激增
- 网络延迟：缓存操作与数据库操作异步化

一致性级别权衡

| 一致性级别 | 适用场景         | 实现复杂度 |
| ---------- | ---------------- | ---------- |
| 强一致性   | 金融、交易系统   | 高         |
| 最终一致性 | 大多数业务场景   | 中         |
| 弱一致性   | 日志、统计类数据 | 低         |

核心原则

1. 业务驱动选择一致性级别
1. 缓存是加速层，不是数据源
1. 设计降级策略，保证可用性
1. 完善监控，及时发现一致性问题

| 场景                     | 推荐方案              | 一致性级别 |
| ------------------------ | --------------------- | ---------- |
| 读写频繁、对一致性要求高 | 双删策略 + 分布式锁   | 高         |
| 读取频繁、写入较少       | Cache-Aside           | 中         |
| 跨服务数据同步           | 消息队列/事件驱动     | 最终一致性 |
| 超大规模系统             | Binlog订阅 + 分级缓存 | 最终一致性 |
| 关键金融交易             | 强一致事务            | 强一致性   |

## 常用方案

### Cache-Aside Pattern（旁路缓存）

适用场景：读多写少的应用

操作流程：

```
读数据：
1. 尝试从Cache获取数据
2. 如果Cache命中，直接返回
3. 如果Cache未命中，从DB获取数据
4. 将数据写入Cache后返回

写数据：
1. 先更新数据库
2. 删除缓存
```

关键点：

- 写操作采用先更新DB，再删除Cache，最大化减少不一致窗口期
- 删除缓存而非更新缓存，避免读写并发问题

### 双删策略（Double Delete）

适用场景：对数据一致性要求高的写操作

实现流程：

```
1. 先删除Cache
2. 更新数据库
3. 延迟再删除一次Cache（如500ms后）
```

关键点：

- 延迟删除可有效防止并发读操作的缓存脏数据
- 第二次删除使用异步任务或消息队列

### Write-Through（穿透写入）

适用场景：写操作较多的系统

核心机制：

```
1. 应用程序只和缓存交互
2. 缓存负责同步更新数据库
3. 写操作同时更新缓存和数据库
```

特点：

- 保证强一致性
- 写操作性能较低（需要同步DB）
- 读操作性能高

### Write-Behind（延迟写入）

适用场景：性能敏感、可以接受短暂不一致的场景

核心机制：

```
1. 应用程序只更新缓存
2. 缓存异步批量写入数据库
3. 数据库更新不阻塞业务流程
```

特点：

- 写操作性能极高
- 数据一致性较弱
- 需要完善的错误恢复机制

## 高级方案

### 消息队列+重试机制

实现流程：

```
1. 更新数据库
2. 发送消息到MQ，通知缓存更新
3. 缓存服务消费消息，更新自身状态
4. 添加消息确认机制和重试策略
```

关键点：

- 使用事务消息确保可靠性
- 实现消息幂等性处理
- 设置合理的重试策略和死信队列

### 数据库Binlog订阅

实现流程：

```
1. 订阅数据库Binlog（如Canal、Debezium）
2. 解析Binlog获取数据变更
3. 通知缓存服务更新或删除数据
```

关键点：

- 实时性高，接近准实时同步
- 数据库主从架构时需考虑Binlog源
- 增量同步降低资源消耗

### 分布式锁+缓存更新

适用场景：高并发下的数据更新

实现流程：

```
1. 获取分布式锁（Redis实现）
2. 更新数据库
3. 更新/删除缓存
4. 释放锁
```

关键点：

- 使用Redis SET NX + PX 实现锁
- 设置合理的锁超时时间
- 实现锁续期机制避免任务未完成锁过期

## 最佳实践

### 读写分离架构

问题：主从延迟导致数据不一致

解决方案：

1. 缓存只读主库数据
1. 写操作强制走主库
1. 设置合理的缓存失效时间
1. 关键数据采用双删策略

### 分库分表场景

问题：跨分片数据一致性问题

解决方案：

1. 综合业务：尽量保证分片内数据操作
1. 跨分片操作：采用分布式事务（如TCC、Saga）
1. 缓存设计：按分片独立管理缓存
1. 数据一致性校验：定期对账

### 高并发场景

问题：并发读写导致的数据覆盖

解决方案：

1. 并发更新：乐观锁或悲观锁
1. 缓存粒度：细化缓存单元（如按ID而非整个集合）
1. 热点数据：单独缓存策略
1. 降级策略：缓存不可用时直接查询DB

## 辅助策略

### 缓存设计优化

缓存粒度：设计合理的缓存单元，避免大粒度缓存
缓存过期：设置不同的TTL，避免同时失效
缓存预热：系统启动时预加载热点数据
空值缓存：对查询结果为空的数据缓存空值，防止缓存穿透

### 监控与告警

一致性监控：定时校验缓存与数据库数据差异
性能监控：监控缓存命中率、响应时间
异常告警：缓存服务不可用、数据不一致等告警

### 应急处理

降级方案：缓存不可用时直接查询数据库
修复工具：开发手动修复缓存数据的工具
回滚机制：更新失败时的回滚策略
